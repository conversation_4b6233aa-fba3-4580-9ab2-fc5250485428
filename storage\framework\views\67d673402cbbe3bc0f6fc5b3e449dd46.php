<?php $__env->startSection('title', 'Manage Products - Admin - Kanha Fashion Hub'); ?>

<?php $__env->startSection('content'); ?>
<!-- Page Header -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h1 class="h3 mb-0 text-gray-800 font-playfair">Product Management</h1>
        <p class="mb-0 text-muted">Manage your jewelry inventory and products</p>
    </div>
    <div>
        <a href="<?php echo e(route('admin.products.create')); ?>" class="btn btn-primary-pink">
            <i class="fas fa-plus me-2"></i>Add New Product
        </a>
    </div>
</div>

<!-- Filters and Search -->
<div class="card border-0 shadow-sm mb-4">
    <div class="card-body">
        <div class="row g-3 align-items-center">
            <div class="col-md-4">
                <div class="input-group">
                    <input type="text" class="form-control" placeholder="Search products...">
                    <button class="btn btn-outline-secondary" type="button">
                        <i class="fas fa-search"></i>
                    </button>
                </div>
            </div>
            <div class="col-md-2">
                <select class="form-select">
                    <option value="">All Categories</option>
                    <option value="rings">Rings</option>
                    <option value="necklaces">Necklaces</option>
                    <option value="earrings">Earrings</option>
                    <option value="bracelets">Bracelets</option>
                </select>
            </div>
            <div class="col-md-2">
                <select class="form-select">
                    <option value="">All Status</option>
                    <option value="active">Active</option>
                    <option value="inactive">Inactive</option>
                    <option value="out-of-stock">Out of Stock</option>
                </select>
            </div>
            <div class="col-md-2">
                <select class="form-select">
                    <option value="">Sort by</option>
                    <option value="name">Name</option>
                    <option value="price">Price</option>
                    <option value="stock">Stock</option>
                    <option value="created">Date Added</option>
                </select>
            </div>
            <div class="col-md-2">
                <button class="btn btn-outline-primary w-100">
                    <i class="fas fa-filter me-1"></i>Filter
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Products Table -->
<div class="card border-0 shadow-sm mb-4">
            <div class="card-header bg-light d-flex justify-content-between align-items-center">
                <h5 class="mb-0">Products (<?php echo e($totalProducts); ?> total)</h5>
                <div class="d-flex gap-2">
                    <button class="btn btn-outline-secondary btn-sm">
                        <i class="fas fa-download me-1"></i>Export
                    </button>
                    <button class="btn btn-outline-danger btn-sm" id="bulk-delete-btn" disabled>
                        <i class="fas fa-trash me-1"></i>Bulk Delete (<span id="selected-count">0</span>)
                    </button>
                </div>
            </div>
            <div class="card-body p-0">
                <div class="table-responsive">
                    <table class="table table-hover mb-0">
                        <thead class="table-light">
                            <tr>
                                <th>
                                    <input type="checkbox" class="form-check-input" id="select-all-products">
                                </th>
                                <th>Product</th>
                                <th>Category</th>
                                <th>Price</th>
                                <th>Stock</th>
                                <th>Status</th>
                                <th>Featured</th>
                                <th>Created</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php $__empty_1 = true; $__currentLoopData = $products; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $product): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                            <tr>
                                <td>
                                    <input type="checkbox" class="form-check-input product-checkbox" value="<?php echo e($product->id); ?>" data-product-name="<?php echo e($product->name); ?>">
                                </td>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <img src="<?php echo e($product->main_image_url); ?>"
                                             alt="<?php echo e($product->name); ?>" class="rounded me-3" width="50" height="50"
                                             onerror="this.src='data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIHZpZXdCb3g9IjAgMCA1MCA1MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjUwIiBoZWlnaHQ9IjUwIiBmaWxsPSIjRjhGOUZBIi8+CjxwYXRoIGQ9Ik0yNSAyMEMyNi4zODA3IDIwIDI3LjUgMjEuMTE5MyAyNy41IDIyLjVDMjcuNSAyMy44ODA3IDI2LjM4MDcgMjUgMjUgMjVDMjMuNjE5MyAyNSAyMi41IDIzLjg4MDcgMjIuNSAyMi41QzIyLjUgMjEuMTE5MyAyMy42MTkzIDIwIDI1IDIwWiIgZmlsbD0iIzZDNzU3RCIvPgo8cGF0aCBkPSJNMTUgMzVMMjAgMzBMMjUgMzVMMzAgMjVMMzUgMzVIMTVaIiBmaWxsPSIjNkM3NTdEIi8+Cjwvc3ZnPgo='; this.onerror=null;">
                                        <div>
                                            <h6 class="mb-0"><?php echo e($product->name); ?></h6>
                                            <small class="text-muted">SKU: <?php echo e($product->sku); ?></small>
                                        </div>
                                    </div>
                                </td>
                                <td><?php echo e($product->category->name ?? 'No Category'); ?></td>
                                <td>
                                    <strong>₹<?php echo e(number_format($product->price)); ?></strong>
                                    <?php if($product->sale_price): ?>
                                        <br><small class="text-success">Sale: ₹<?php echo e(number_format($product->sale_price)); ?></small>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <?php if($product->stock_quantity > 10): ?>
                                        <span class="badge bg-success"><?php echo e($product->stock_quantity); ?> in stock</span>
                                    <?php elseif($product->stock_quantity > 0): ?>
                                        <span class="badge bg-warning"><?php echo e($product->stock_quantity); ?> in stock</span>
                                    <?php else: ?>
                                        <span class="badge bg-danger">Out of Stock</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <?php if($product->status === 'active'): ?>
                                        <span class="badge bg-success">Active</span>
                                    <?php else: ?>
                                        <span class="badge bg-secondary">Inactive</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <?php if($product->is_featured): ?>
                                        <span class="badge" style="background-color: var(--primary-brown); color: var(--primary-cream);">
                                            <i class="fas fa-star me-1"></i>Featured
                                        </span>
                                    <?php else: ?>
                                        <span class="text-muted">-</span>
                                    <?php endif; ?>
                                </td>
                                <td><?php echo e($product->created_at->format('M d, Y')); ?></td>
                                <td>
                                    <div class="dropdown">
                                        <button class="btn btn-outline-secondary btn-sm dropdown-toggle" data-bs-toggle="dropdown">
                                            Actions
                                        </button>
                                        <ul class="dropdown-menu">
                                            <li><a class="dropdown-item" href="#">
                                                <i class="fas fa-eye me-2"></i>View
                                            </a></li>
                                            <li><a class="dropdown-item" href="<?php echo e(route('admin.products.edit', $product->id)); ?>">
                                                <i class="fas fa-edit me-2"></i>Edit
                                            </a></li>
                                            <li><a class="dropdown-item" href="#">
                                                <i class="fas fa-copy me-2"></i>Duplicate
                                            </a></li>
                                            <li><hr class="dropdown-divider"></li>
                                            <li><a class="dropdown-item text-danger delete-product" href="#" data-id="<?php echo e($product->id); ?>" data-name="<?php echo e($product->name); ?>">
                                                <i class="fas fa-trash me-2"></i>Delete
                                            </a></li>
                                        </ul>
                                    </div>
                                </td>
                            </tr>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                            <tr>
                                <td colspan="8" class="text-center py-4">
                                    <i class="fas fa-gem fa-3x text-muted mb-3"></i>
                                    <h5 class="text-muted">No Products Found</h5>
                                    <p class="text-muted mb-4">Start by adding your first jewelry product.</p>
                                    <a href="<?php echo e(route('admin.products.create')); ?>" class="btn btn-primary-pink">
                                        <i class="fas fa-plus me-2"></i>Add First Product
                                    </a>
                                </td>
                            </tr>
                            <?php endif; ?>
                        </tbody>
                    </table>
                </div>
            </div>
            
            <!-- Pagination -->
            <?php if($products->hasPages()): ?>
            <div class="card-footer bg-light">
                <div class="row align-items-center">
                    <div class="col-md-6">
                        <p class="text-muted mb-0">
                            Showing <?php echo e($products->firstItem()); ?> to <?php echo e($products->lastItem()); ?>

                            of <?php echo e($products->total()); ?> products
                        </p>
                    </div>
                    <div class="col-md-6">
                        <?php echo e($products->links('pagination::bootstrap-5')); ?>

                    </div>
                </div>
            </div>
            <?php endif; ?>
        </div>

<!-- Quick Stats -->
<div class="card border-0 shadow-sm">
    <div class="card-header bg-light">
        <h5 class="mb-0">Product Statistics</h5>
    </div>
    <div class="card-body">
        <div class="row g-4">
            <div class="col-lg-3 col-md-6">
                <div class="text-center p-3 bg-light rounded">
                    <i class="fas fa-gem text-primary-pink fs-2 mb-2"></i>
                    <h4 class="font-playfair text-primary-pink mb-1"><?php echo e($totalProducts); ?></h4>
                    <p class="text-muted mb-0 small">Total Products</p>
                </div>
            </div>
            <div class="col-lg-3 col-md-6">
                <div class="text-center p-3 bg-light rounded">
                    <i class="fas fa-check-circle text-success fs-2 mb-2"></i>
                    <h4 class="font-playfair text-success mb-1"><?php echo e($activeProducts); ?></h4>
                    <p class="text-muted mb-0 small">Active Products</p>
                </div>
            </div>
            <div class="col-lg-3 col-md-6">
                <div class="text-center p-3 bg-light rounded">
                    <i class="fas fa-exclamation-triangle text-warning fs-2 mb-2"></i>
                    <h4 class="font-playfair text-warning mb-1"><?php echo e($lowStockProducts); ?></h4>
                    <p class="text-muted mb-0 small">Low Stock</p>
                </div>
            </div>
            <div class="col-lg-3 col-md-6">
                <div class="text-center p-3 bg-light rounded">
                    <i class="fas fa-times-circle text-danger fs-2 mb-2"></i>
                    <h4 class="font-playfair text-danger mb-1"><?php echo e($outOfStockProducts); ?></h4>
                    <p class="text-muted mb-0 small">Out of Stock</p>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Bulk Delete Confirmation Modal -->
<div class="modal fade" id="bulkDeleteModal" tabindex="-1" aria-labelledby="bulkDeleteModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="bulkDeleteModalLabel">
                    <i class="fas fa-exclamation-triangle text-warning me-2"></i>
                    Confirm Bulk Delete
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p class="mb-3">Are you sure you want to delete the following <strong id="modal-product-count">0</strong> product(s)?</p>
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <strong>Warning:</strong> This action cannot be undone. All related data (cart items, wishlists, etc.) will also be deleted.
                </div>
                <div class="mb-3">
                    <strong>Products to be deleted:</strong>
                    <ul id="modal-product-list" class="mt-2 mb-0" style="max-height: 200px; overflow-y: auto;">
                    </ul>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-danger" id="confirm-bulk-delete">
                    <i class="fas fa-trash me-2"></i>Delete Products
                </button>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Select All functionality
    const selectAllCheckbox = document.getElementById('select-all-products');
    const productCheckboxes = document.querySelectorAll('.product-checkbox');
    const bulkDeleteBtn = document.getElementById('bulk-delete-btn');
    const selectedCountSpan = document.getElementById('selected-count');

    // Update selected count and bulk delete button state
    function updateBulkDeleteState() {
        const checkedBoxes = document.querySelectorAll('.product-checkbox:checked');
        const count = checkedBoxes.length;

        selectedCountSpan.textContent = count;
        bulkDeleteBtn.disabled = count === 0;

        // Update select all checkbox state
        if (count === 0) {
            selectAllCheckbox.indeterminate = false;
            selectAllCheckbox.checked = false;
        } else if (count === productCheckboxes.length) {
            selectAllCheckbox.indeterminate = false;
            selectAllCheckbox.checked = true;
        } else {
            selectAllCheckbox.indeterminate = true;
            selectAllCheckbox.checked = false;
        }
    }

    // Select all checkbox handler
    selectAllCheckbox.addEventListener('change', function() {
        const isChecked = this.checked;
        productCheckboxes.forEach(checkbox => {
            checkbox.checked = isChecked;
        });
        updateBulkDeleteState();
    });

    // Individual checkbox handlers
    productCheckboxes.forEach(checkbox => {
        checkbox.addEventListener('change', updateBulkDeleteState);
    });

    // Bulk delete handler
    bulkDeleteBtn.addEventListener('click', function() {
        const checkedBoxes = document.querySelectorAll('.product-checkbox:checked');
        const productIds = Array.from(checkedBoxes).map(cb => cb.value);
        const productNames = Array.from(checkedBoxes).map(cb => cb.dataset.productName);

        if (productIds.length === 0) {
            alert('Please select products to delete.');
            return;
        }

        // Populate modal with product information
        document.getElementById('modal-product-count').textContent = productIds.length;
        const productList = document.getElementById('modal-product-list');
        productList.innerHTML = '';
        productNames.forEach(name => {
            const li = document.createElement('li');
            li.textContent = name;
            productList.appendChild(li);
        });

        // Show modal
        const modal = new bootstrap.Modal(document.getElementById('bulkDeleteModal'));
        modal.show();
    });

    // Confirm bulk delete from modal
    document.getElementById('confirm-bulk-delete').addEventListener('click', function() {
        const checkedBoxes = document.querySelectorAll('.product-checkbox:checked');
        const productIds = Array.from(checkedBoxes).map(cb => cb.value);

        if (productIds.length === 0) {
            return;
        }
        // Hide modal
        const modal = bootstrap.Modal.getInstance(document.getElementById('bulkDeleteModal'));
        modal.hide();

        // Disable bulk delete button and show loading state
        bulkDeleteBtn.disabled = true;
        const originalBulkBtnContent = bulkDeleteBtn.innerHTML;
        bulkDeleteBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Deleting...';

        // Disable confirm button and show loading state
        this.disabled = true;
        this.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Deleting...';

        fetch('/admin/products/bulk-delete', {
                method: 'POST',
                headers: {
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                    'Content-Type': 'application/json',
                    'Accept': 'application/json'
                },
                body: JSON.stringify({
                    product_ids: productIds
                })
            })
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                return response.json();
            })
            .then(data => {
                if (data.success) {
                    // Show success message
                    const alert = document.createElement('div');
                    alert.className = 'alert alert-success alert-dismissible fade show';
                    alert.innerHTML = `
                        ${data.message}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    `;
                    document.querySelector('.content-wrapper').insertBefore(alert, document.querySelector('.content-wrapper').firstChild);

                    // Remove deleted rows
                    checkedBoxes.forEach(checkbox => {
                        checkbox.closest('tr').remove();
                    });

                    // Reset states
                    updateBulkDeleteState();

                    // Reload page after delay to update statistics
                    setTimeout(() => {
                        location.reload();
                    }, 2000);
                } else {
                    alert('Error: ' + data.message);
                    // Reset button states
                    bulkDeleteBtn.innerHTML = originalBulkBtnContent;
                    updateBulkDeleteState();
                    this.disabled = false;
                    this.innerHTML = '<i class="fas fa-trash me-2"></i>Delete Products';
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('An error occurred while deleting products: ' + error.message);
                // Reset button states
                bulkDeleteBtn.innerHTML = originalBulkBtnContent;
                updateBulkDeleteState();
                this.disabled = false;
                this.innerHTML = '<i class="fas fa-trash me-2"></i>Delete Products';
            });
    });

    // Initialize state
    updateBulkDeleteState();

    // Handle delete product (individual)
    document.querySelectorAll('.delete-product').forEach(button => {
        button.addEventListener('click', function(e) {
            e.preventDefault();

            const productId = this.dataset.id;
            const productName = this.dataset.name;

            if (confirm(`Are you sure you want to delete "${productName}"? This action cannot be undone.`)) {
                fetch(`/admin/products/${productId}`, {
                    method: 'DELETE',
                    headers: {
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                        'Content-Type': 'application/json'
                    }
                })
                .then(response => {
                    console.log('Response status:', response.status);
                    if (!response.ok) {
                        throw new Error(`HTTP error! status: ${response.status}`);
                    }
                    return response.json();
                })
                .then(data => {
                    console.log('Response data:', data);
                    if (data.success) {
                        // Show success message
                        const alert = document.createElement('div');
                        alert.className = 'alert alert-success alert-dismissible fade show';
                        alert.innerHTML = `
                            ${data.message}
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        `;
                        document.querySelector('.content-wrapper').insertBefore(alert, document.querySelector('.content-wrapper').firstChild);

                        // Remove the row
                        this.closest('tr').remove();

                        // Update statistics if needed
                        setTimeout(() => {
                            location.reload();
                        }, 2000);
                    } else {
                        alert('Error: ' + data.message);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('An error occurred while deleting the product: ' + error.message);
                });
            }
        });
    });
});
</script>
<?php $__env->stopPush(); ?>

<?php $__env->startPush('styles'); ?>
<style>
    .pagination .page-link {
        color: var(--primary-brown);
        border-color: var(--primary-brown);
    }

    .pagination .page-item.active .page-link {
        background-color: var(--primary-brown);
        border-color: var(--primary-brown);
    }

    .pagination .page-link:hover {
        background-color: var(--secondary-brown);
        border-color: var(--secondary-brown);
        color: white;
    }
</style>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.admin', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\kanha-shop-web\resources\views/admin/products/index.blade.php ENDPATH**/ ?>