<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * @property int $id
 * @property int $user_id
 * @property string $session_id
 * @property int $product_id
 * @property int $quantity
 * @property string $size
 * @property float $total_price
 * @property \Illuminate\Support\Carbon $created_at
 * @property \Illuminate\Support\Carbon $updated_at
 *
 * @property-read \App\Models\User $user
 * @property-read \App\Models\Product $product
 *
 * @method static \Illuminate\Database\Eloquent\Collection getCartItems(int $userId = null, string $sessionId = null)
 * @method static float getCartTotal(int $userId = null, string $sessionId = null)
 * @method static int getCartCount(int $userId = null, string $sessionId = null)
 */
class CartItem extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'session_id',
        'product_id',
        'quantity',
        'size_id',
        'size_old', // For migration purposes
        'options',
    ];

    protected $casts = [
        'options' => 'array',
    ];

    // Relationships
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function product()
    {
        return $this->belongsTo(Product::class);
    }

    public function size()
    {
        return $this->belongsTo(Size::class);
    }

    public function productSize()
    {
        return $this->hasOne(ProductSize::class, 'product_id', 'product_id')
                    ->where('size_id', $this->size_id);
    }

    // Helper methods
    public function getTotalPriceAttribute()
    {
        $basePrice = $this->product->current_price;

        // Add size-specific price adjustment if applicable
        if ($this->size_id) {
            $productSize = $this->product->getProductSize($this->size_id);
            if ($productSize) {
                $basePrice += $productSize->price_adjustment;
            }
        }

        return $basePrice * $this->quantity;
    }

    /**
     * Get the selected size name for display
     */
    public function getSizeNameAttribute()
    {
        if ($this->size_id && $this->size) {
            return $this->size->full_display_name;
        }

        // Fallback to old size field during migration
        return $this->size_old;
    }

    /**
     * Check if this cart item has sufficient stock
     */
    public function hasSufficientStock()
    {
        if ($this->size_id) {
            return $this->product->hasSufficientStock($this->quantity, $this->size_id);
        }

        // Legacy stock check
        return $this->product->hasSufficientStock($this->quantity);
    }

    public static function getCartItems($userId = null, $sessionId = null)
    {
        $query = static::with('product');

        if ($userId) {
            $query->where('user_id', $userId);
        } elseif ($sessionId) {
            $query->where('session_id', $sessionId);
        }

        return $query->get();
    }

    public static function getCartTotal($userId = null, $sessionId = null)
    {
        $items = static::getCartItems($userId, $sessionId);
        return $items->sum('total_price');
    }

    public static function getCartCount($userId = null, $sessionId = null)
    {
        $query = static::query();

        if ($userId) {
            $query->where('user_id', $userId);
        } elseif ($sessionId) {
            $query->where('session_id', $sessionId);
        }

        return $query->sum('quantity');
    }

    /**
     * Transfer session cart to user cart when user logs in
     */
    public static function transferSessionCartToUser($userId, $sessionId)
    {
        if (!$userId || !$sessionId) {
            return false;
        }

        // Get session cart items
        $sessionItems = static::where('session_id', $sessionId)->get();

        if ($sessionItems->isEmpty()) {
            return true; // Nothing to transfer
        }

        foreach ($sessionItems as $sessionItem) {
            // Check if user already has this product in cart
            $existingUserItem = static::where('user_id', $userId)
                ->where('product_id', $sessionItem->product_id)
                ->where('size', $sessionItem->size)
                ->first();

            if ($existingUserItem) {
                // Merge quantities
                $existingUserItem->update([
                    'quantity' => $existingUserItem->quantity + $sessionItem->quantity
                ]);
            } else {
                // Transfer session item to user
                $sessionItem->update([
                    'user_id' => $userId,
                    'session_id' => null
                ]);
            }
        }

        // Delete any remaining session items
        static::where('session_id', $sessionId)->delete();

        return true;
    }

    /**
     * Clear cart for user or session
     */
    public static function clearCart($userId = null, $sessionId = null)
    {
        $query = static::query();

        if ($userId) {
            $query->where('user_id', $userId);
        } elseif ($sessionId) {
            $query->where('session_id', $sessionId);
        }

        return $query->delete();
    }

    /**
     * Get cart summary with totals
     */
    public static function getCartSummary($userId = null, $sessionId = null)
    {
        $items = static::getCartItems($userId, $sessionId);

        $subtotal = 0;
        $discount = 0;
        $itemCount = 0;

        foreach ($items as $item) {
            $currentPrice = $item->product->isOnSale() ? $item->product->sale_price : $item->product->price;
            $itemCount += $item->quantity;

            // Use current price (sale price if on sale) for subtotal calculation
            $itemTotal = $currentPrice * $item->quantity;
            $subtotal += $itemTotal;

            // Don't calculate separate discount - the subtotal already reflects the sale price
            // This prevents the double-discount issue where discount > subtotal
        }

        // Calculate shipping using dynamic settings
        $freeShippingThreshold = \App\Models\Setting::get('free_shipping_threshold', 25000);
        $shippingCharge = \App\Models\Setting::get('shipping_charge', 500);
        $shipping = $subtotal >= $freeShippingThreshold ? 0 : $shippingCharge;

        // Calculate tax (3% GST) - ensure taxable amount is never negative
        $taxableAmount = max(0, $subtotal - $discount);
        $tax = $taxableAmount * 0.03;

        // Calculate total
        $total = $subtotal - $discount + $shipping + $tax;

        return [
            'items' => $items,
            'item_count' => $itemCount,
            'subtotal' => $subtotal,
            'discount' => $discount,
            'shipping' => $shipping,
            'tax' => $tax,
            'total' => $total,
            'free_shipping_eligible' => $subtotal >= $freeShippingThreshold,
            'free_shipping_remaining' => max(0, $freeShippingThreshold - $subtotal)
        ];
    }
}
