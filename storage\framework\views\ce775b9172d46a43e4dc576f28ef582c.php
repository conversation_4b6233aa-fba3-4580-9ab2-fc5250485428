<?php $__env->startSection('title', 'Kanha Fashion Hub - Exquisite Fashion Collection | Premium Handmade Fashion Jewelry'); ?>
<?php $__env->startSection('description', 'Discover beautiful handcrafted fashion jewelry at Kanha Fashion Hub. Premium rings, necklaces, earrings, and bracelets with free shipping across India. Shop authentic handmade fashion jewelry.'); ?>
<?php $__env->startSection('keywords', 'fashion jewelry, rings, necklaces, earrings, bracelets, handmade jewelry, fashion accessories, Indian fashion jewelry, wedding jewelry, engagement rings, traditional jewelry, modern jewelry'); ?>
<?php $__env->startSection('og_title', 'Kanha Fashion Hub - Exquisite Fashion Collection'); ?>
<?php $__env->startSection('og_description', 'Discover beautiful handcrafted fashion jewelry at Kanha Fashion Hub. Premium rings, necklaces, earrings, and bracelets with free shipping across India.'); ?>
<?php $__env->startSection('content'); ?>
<!-- Hero Section -->
<!-- Hero Section -->
<section id="home" class="hero-section">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-6">
                <h1 class="font-modern fw-bold mb-4 brand-text">
                    <span class="font-julius" style="color: var(--primary-pink);">Kanha Fashion Hub</span>
                </h1>
                <p class="lead mb-4 text-muted font-body">
                    At Kanha Fashion Hub, we supply premium fashion jewellery that combines elegance, quality, and modern design. Our collections are crafted with skin-friendly, durable materials — no gold or silver, just pure style and exceptional craftsmanship.
                </p>
                <div class="mb-4">
                    <h5 class="font-cursive-bold tagline-text mb-2" style="color: var(--primary-brown);">✨ Reliable. Stylish. Trusted.</h5>
                    <p class="text-muted font-body" style="font-style: italic; font-weight: 300;">Your brand, our craftsmanship.</p>
                </div>
                <div class="d-flex flex-wrap gap-3">
                    <a href="<?php echo e(url('/collections')); ?>" class="btn font-modern" style="background-color: var(--primary-brown); color: var(--primary-cream); border-radius: 25px; padding: 12px 24px; font-weight: 600;">
                        <i class="fas fa-gem me-2"></i>Shop Collection
                    </a>
                    <button type="button" class="btn btn-outline-pink" data-bs-toggle="modal" data-bs-target="#storyVideoModal">
                        <i class="fas fa-play me-2"></i>Our Story
                    </button>
                </div>

                <!-- Stats -->
                
            </div>

            <div class="col-lg-6 mt-3">
                <div class="hero-image-container position-relative">
                    <!-- Main Hero Image -->
                    <div class="hero-image-wrapper">
                        <img src="<?php echo e(asset('images/homepagepic.jpg')); ?>"
                             alt="Kanha Fashion Hub - Exquisite Jewelry Collection"
                             class="img-fluid rounded-4 shadow-lg hero-main-image"
                             onerror="this.src='https://images.unsplash.com/photo-1515562141207-7a88fb7ce338?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80'">
                    </div>

                    <!-- Floating Feature Cards -->
                    
                </div>

                <!-- Mobile Feature Cards -->
                
            </div>
        </div>
    </div>
</section>

<!-- Featured Collections -->
<section id="collections" class="py-5">
    <div class="container">
        <h2 class="section-title">Featured Collections</h2>

        <div class="row g-3 g-md-4">
            <?php if(isset($featuredCategories) && $featuredCategories->count() > 0): ?>
                <?php $__currentLoopData = $featuredCategories; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $category): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <div class="col-12 col-sm-6 col-lg-3">
                        <div class="card product-card h-100 border-0 shadow-sm">
                            <div class="position-relative overflow-hidden">
                                <img src="<?php echo e($category->image_url); ?>"
                                     class="card-img-top" alt="<?php echo e($category->name); ?> Collection"
                                     style="height: 250px; object-fit: cover;"
                                     onerror="this.src='https://images.unsplash.com/photo-1515562141207-7a88fb7ce338?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80'">
                                <div class="product-overlay">
                                    <a href="<?php echo e(route('collections.category', $category->slug)); ?>" class="btn btn-light rounded-pill">
                                        <i class="fas fa-eye me-2"></i>View Collection
                                    </a>
                                </div>
                            </div>
                            <div class="card-body text-center">
                                <h5 class="card-title font-playfair"><?php echo e($category->name); ?></h5>
                                <p class="card-text text-muted"><?php echo e($category->description); ?></p>
                                <div class="d-flex justify-content-center align-items-center">
                                    <?php
                                        $minPrice = $category->products()->active()->inStock()->min('price');
                                        $productCount = $category->products()->active()->inStock()->count();
                                    ?>
                                    <?php if($minPrice): ?>
                                        <span class="fw-bold font-modern" style="color: var(--primary-brown);">From ₹<?php echo e(number_format($minPrice)); ?></span>
                                    <?php endif; ?>
                                    <?php if($productCount > 0): ?>
                                        <small class="text-muted ms-2">(<?php echo e($productCount); ?> items)</small>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    </div>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            <?php else: ?>
                <!-- Fallback static collections if no featured categories found -->
                <div class="col-12 col-sm-6 col-lg-3">
                    <div class="card product-card h-100 border-0 shadow-sm">
                        <div class="position-relative overflow-hidden">
                            <img src="https://images.unsplash.com/photo-1605100804763-247f67b3557e?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80"
                                 class="card-img-top" alt="Rings Collection" style="height: 250px; object-fit: cover;">
                            <div class="product-overlay">
                                <a href="<?php echo e(route('collections')); ?>" class="btn btn-light rounded-pill">
                                    <i class="fas fa-eye me-2"></i>View Collection
                                </a>
                            </div>
                        </div>
                        <div class="card-body text-center">
                            <h5 class="card-title font-playfair">Elegant Rings</h5>
                            <p class="card-text text-muted">Discover our stunning ring collection</p>
                        </div>
                    </div>
                </div>
            <?php endif; ?>
        </div>

        <div class="text-center mt-5">
            <a href="<?php echo e(route('collections')); ?>" class="btn btn-outline-pink btn-lg">
                <i class="fas fa-th me-2"></i>View All Collections
            </a>
        </div>
    </div>
</section>

<!-- Featured Products -->
<section class="py-5 bg-light">
    <div class="container">
        <h2 class="section-title">Featured Products</h2>
        <p class="text-center text-muted mb-5">Handpicked pieces from our exclusive collection</p>

        <?php if(isset($featuredProducts) && $featuredProducts->count() > 0): ?>
            <div class="row g-3 g-md-4">
                <?php $__currentLoopData = $featuredProducts->take(4); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $product): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <div class="col-12 col-sm-6 col-lg-3">
                        <div class="card product-card h-100 border-0 shadow-sm">
                            <div class="position-relative overflow-hidden">
                                <?php if($product->images && count($product->images) > 0): ?>
                                    <?php
                                        $imagePath = $product->images[0];
                                        // Check if it's a full URL
                                        if (filter_var($imagePath, FILTER_VALIDATE_URL)) {
                                            $imageUrl = $imagePath;
                                        } else {
                                            // Clean the path and ensure storage/ prefix
                                            $cleanPath = str_replace(['\\/', '\\'], '/', $imagePath);
                                            // Add storage/ prefix if not already present
                                            if (!str_starts_with($cleanPath, 'storage/')) {
                                                $cleanPath = 'storage/' . ltrim($cleanPath, '/');
                                            }
                                            $imageUrl = asset($cleanPath);
                                        }
                                    ?>
                                    <img src="<?php echo e($imageUrl); ?>"
                                         alt="<?php echo e($product->name); ?>"
                                         class="card-img-top"
                                         style="height: 250px; object-fit: cover;"
                                         onerror="this.src='https://images.unsplash.com/photo-1515562141207-7a88fb7ce338?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80'">
                                <?php else: ?>
                                    <img src="https://images.unsplash.com/photo-1515562141207-7a88fb7ce338?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80"
                                         alt="<?php echo e($product->name); ?>"
                                         class="card-img-top"
                                         style="height: 250px; object-fit: cover;">
                                <?php endif; ?>

                                <!-- Product badges -->
                                <div class="position-absolute top-0 start-0 p-2">
                                    <span class="badge" style="background-color: var(--primary-brown); color: var(--primary-cream);">Featured</span>
                                    <?php if($product->isOnSale()): ?>
                                        <span class="badge bg-warning ms-1">Sale</span>
                                    <?php endif; ?>
                                </div>

                                <!-- Removed overlay for cleaner design -->
                            </div>

                            <div class="card-body text-center">
                                <div class="mb-2">
                                    <small class="text-muted"><?php echo e($product->category->name); ?></small>
                                </div>
                                <h5 class="card-title font-playfair mb-2"><?php echo e($product->name); ?></h5>
                                <p class="card-text text-muted small mb-3"><?php echo e(Str::limit($product->short_description, 60)); ?></p>

                                <div class="d-flex justify-content-center align-items-center mb-3">
                                    <?php if($product->isOnSale()): ?>
                                        <span class="fw-bold me-2 font-modern" style="color: var(--primary-brown);">₹<?php echo e(number_format($product->sale_price)); ?></span>
                                        <span class="text-muted text-decoration-line-through small">₹<?php echo e(number_format($product->price)); ?></span>
                                    <?php else: ?>
                                        <span class="fw-bold font-modern" style="color: var(--primary-brown);">₹<?php echo e(number_format($product->price)); ?></span>
                                    <?php endif; ?>
                                </div>

                                <!-- Price Display -->
                                <div class="mb-3">
                                    <?php if($product->isOnSale()): ?>
                                        <div class="d-flex align-items-center justify-content-center gap-2">
                                            <span class="h5 mb-0 text-success fw-bold">₹<?php echo e(number_format($product->sale_price)); ?></span>
                                            <small class="text-muted text-decoration-line-through">₹<?php echo e(number_format($product->price)); ?></small>
                                        </div>
                                    <?php else: ?>
                                        <div class="text-center">
                                            <span class="h5 mb-0 fw-bold">₹<?php echo e(number_format($product->price)); ?></span>
                                        </div>
                                    <?php endif; ?>
                                </div>

                                <!-- Add to Cart Button -->
                                <?php if($product->in_stock): ?>
                                    <button class="btn btn-primary-pink w-100 mb-2 add-to-cart-btn"
                                        data-product-id="<?php echo e($product->id); ?>" style="font-weight: 600; padding: 12px;">
                                        <i class="fas fa-shopping-cart me-2"></i>Add to Cart
                                    </button>
                                    <div class="d-flex gap-2 mb-2">
                                        <a href="<?php echo e(route('product.detail', $product->slug)); ?>"
                                           class="btn btn-outline-primary btn-sm flex-fill">
                                            <i class="fas fa-eye me-1"></i>View Details
                                        </a>
                                        <button class="btn btn-outline-secondary btn-sm flex-fill wishlist-btn"
                                            data-product-id="<?php echo e($product->id); ?>">
                                            <i class="fas fa-heart me-1"></i>Wishlist
                                        </button>
                                    </div>
                                    <span class="badge bg-success-subtle text-success">In Stock</span>
                                <?php else: ?>
                                    <button class="btn btn-secondary w-100 mb-2" disabled>
                                        <i class="fas fa-times me-2"></i>Out of Stock
                                    </button>
                                    <a href="<?php echo e(route('product.detail', $product->slug)); ?>"
                                       class="btn btn-outline-primary btn-sm w-100 mb-2">
                                        <i class="fas fa-eye me-1"></i>View Details
                                    </a>
                                    <span class="badge bg-danger-subtle text-danger">Out of Stock</span>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </div>

            <div class="text-center mt-5">
                <a href="<?php echo e(route('collections')); ?>?sort=featured" class="btn btn-lg font-modern" style="background-color: var(--primary-brown); color: var(--primary-cream); border-radius: 25px; padding: 15px 30px; font-weight: 600;">
                    <i class="fas fa-star me-2"></i>View All Featured Products
                </a>
            </div>
        <?php else: ?>
            <div class="text-center py-5">
                <i class="fas fa-gem text-muted mb-3" style="font-size: 3rem;"></i>
                <h4 class="text-muted">No Featured Products Available</h4>
                <p class="text-muted">Check back soon for our handpicked collection!</p>
                <a href="<?php echo e(route('collections')); ?>" class="btn btn-outline-pink">
                    <i class="fas fa-th me-2"></i>Browse All Products
                </a>
            </div>
        <?php endif; ?>
    </div>
</section>

<!-- Testimonials Section -->


<!-- Our Story Video Modal -->
<div class="modal fade" id="storyVideoModal" tabindex="-1" aria-labelledby="storyVideoModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg modal-dialog-centered">
        <div class="modal-content border-0 shadow-lg">
            <div class="modal-header border-0 pb-0">
                <h5 class="modal-title font-playfair" id="storyVideoModalLabel" style="color: var(--primary-brown);">
                    <i class="fas fa-heart me-2"></i>Our Story
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body p-0">
                <div class="ratio ratio-16x9">
                    <video id="storyVideo" class="rounded-bottom" controls preload="metadata" poster="<?php echo e(asset('images/homepagepic.jpg')); ?>">
                        <source src="<?php echo e(asset('video/kanhastory.mp4')); ?>" type="video/mp4">
                        <p class="text-center p-4">
                            Your browser doesn't support HTML5 video.
                            <a href="<?php echo e(asset('video/kanhastory.mp4')); ?>" download>Download the video</a> instead.
                        </p>
                    </video>
                </div>
            </div>
            <div class="modal-footer border-0 pt-2">
                <div class="text-center w-100">
                    <p class="text-muted mb-2 font-body">
                        <i class="fas fa-sparkles me-1" style="color: var(--primary-brown);"></i>
                        Discover the passion behind every piece
                    </p>
                    <a href="<?php echo e(url('/about')); ?>" class="btn btn-sm font-modern" style="background-color: var(--primary-brown); color: var(--primary-cream); border-radius: 20px; padding: 8px 20px;">
                        <i class="fas fa-arrow-right me-1"></i>Learn More About Us
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('styles'); ?>
<style>
    /* Mobile-First Welcome Page Styles */

    /* Enhanced Typography for Mobile */
    @media (max-width: 576px) {
        .hero-section {
            padding: 2rem 0;
        }

        .hero-section .container {
            padding: 0 1rem;
        }

        .brand-text {
            text-align: center;
            margin-bottom: 1.5rem;
        }

        .font-julius {
            font-size: 1.4rem;
            line-height: 1.2;
            display: block;
            margin-top: 0.5rem;
        }

        .tagline-text {
            text-align: center;
            font-size: 0.95rem;
        }

        .lead {
            font-size: 0.9rem;
            line-height: 1.5;
            text-align: center;
        }

        /* .btn {
            width: 100%;
            margin-bottom: 1rem;
            font-size: 0.9rem;
            padding: 12px 20px;
        } */

        .card-body {
            padding: 1rem 0.75rem;
        }

        .card-title {
            font-size: 1rem;
            line-height: 1.3;
        }

        .card-text {
            font-size: 0.85rem;
            line-height: 1.4;
        }
    }

    /* Tablet Styles */
    @media (min-width: 577px) and (max-width: 768px) {
        .font-julius {
            font-size: 1.8rem;
            line-height: 1.2;
        }

        .tagline-text {
            font-size: 1.1rem;
        }

        .lead {
            font-size: 1rem;
            line-height: 1.6;
        }
    }

    /* Desktop Styles */
    @media (min-width: 769px) {
        .font-julius {
            font-size: 2.2rem;
            line-height: 1.1;
        }

        .tagline-text {
            font-size: 1.3rem;
        }

        .lead {
            font-size: 1.1rem;
            line-height: 1.7;
        }
    }

    /* Enhanced Button Styles */
    .btn {
        transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
        border: none;
        box-shadow: 0 4px 15px rgba(139, 69, 19, 0.2);
    }

    .btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(139, 69, 19, 0.3);
    }

    .btn:active {
        transform: translateY(0);
        box-shadow: 0 2px 10px rgba(139, 69, 19, 0.2);
    }

    /* Enhanced Card Styles */
    .card {
        transition: all 0.3s ease;
        border: none;
        box-shadow: 0 2px 15px rgba(0, 0, 0, 0.08);
    }

    .card:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
    }

    /* Typography Enhancements */
    .font-julius {
        text-rendering: optimizeLegibility;
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
    }

    .font-cursive-bold {
        text-rendering: optimizeLegibility;
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
    }

    .font-modern {
        text-rendering: optimizeLegibility;
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
    }

    .font-body {
        text-rendering: optimizeLegibility;
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
    }

    /* Accessibility Improvements */
    @media (prefers-reduced-motion: reduce) {
        .btn, .card {
            transition: none;
        }

        .btn:hover, .card:hover {
            transform: none;
        }
    }

    /* High DPI Display Optimizations */
    @media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
        .font-julius, .font-cursive-bold, .font-modern, .font-body {
            -webkit-font-smoothing: subpixel-antialiased;
        }
    }

    /* Hero Image Enhancements */
    .hero-image-container {
        position: relative;
        z-index: 1;
    }

    .hero-image-wrapper {
        position: relative;
        overflow: hidden;
        border-radius: 1.5rem;
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    }

    .hero-main-image {
        width: 100%;
        height: auto;
        min-height: 300px;
        object-fit: cover;
        transition: transform 0.3s ease;
    }

    .hero-main-image:hover {
        transform: scale(1.02);
    }

    /* Floating Cards */
    .floating-features {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        pointer-events: none;
        z-index: 2;
    }

    .floating-card {
        position: absolute;
        pointer-events: auto;
        animation: float 6s ease-in-out infinite;
    }

    .floating-card-1 {
        top: 15%;
        left: -10%;
        animation-delay: 0s;
    }

    .floating-card-2 {
        bottom: 20%;
        right: -10%;
        animation-delay: 3s;
    }

    .floating-card .card {
        backdrop-filter: blur(10px);
        background: rgba(255, 255, 255, 0.95);
        border: 1px solid rgba(255, 255, 255, 0.2);
        transform: scale(0.9);
        transition: all 0.3s ease;
    }

    .floating-card .card:hover {
        transform: scale(1);
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
    }

    @keyframes float {
        0%, 100% { transform: translateY(0px); }
        50% { transform: translateY(-10px); }
    }

    /* Video Modal Enhancements */
    .modal-content {
        border-radius: 1rem;
        overflow: hidden;
    }

    .modal-header {
        background: linear-gradient(135deg, rgba(139, 69, 19, 0.05), rgba(233, 30, 99, 0.05));
    }

    .modal-footer {
        background: linear-gradient(135deg, rgba(139, 69, 19, 0.02), rgba(233, 30, 99, 0.02));
    }

    #storyVideo {
        border-radius: 0 0 1rem 1rem;
    }

    /* Mobile Optimizations */
    @media (max-width: 991px) {
        .hero-main-image {
            min-height: 250px;
        }

        .floating-features {
            display: none !important;
        }
    }

    @media (max-width: 576px) {
        .hero-main-image {
            min-height: 200px;
        }

        .modal-dialog {
            margin: 1rem;
        }
    }
</style>
<?php $__env->stopPush(); ?>

<?php $__env->startPush('structured-data'); ?>
<script type="application/ld+json">
{
    "@context": "https://schema.org",
    "@type": "Organization",
    "name": "Kanha Fashion Hub",
    "alternateName": "Kanha Fashion Hub",
    "url": "<?php echo e(url('/')); ?>",
    "logo": "<?php echo e(asset('images/logo.png')); ?>",
    "description": "Kanha Fashion Hub - Exquisite handcrafted fashion jewelry collection. Premium rings, necklaces, earrings, and bracelets with free shipping across India.",
    "address": {
        "@type": "PostalAddress",
        "addressCountry": "IN"
    },
    "contactPoint": {
        "@type": "ContactPoint",
        "contactType": "customer service",
        "availableLanguage": ["English", "Hindi"]
    },
    "sameAs": [
        "https://www.facebook.com/kanhafashionhub",
        "https://www.instagram.com/kanhafashionhub",
        "https://www.twitter.com/kanhafashionhub"
    ]
}
</script>

<script type="application/ld+json">
{
    "@context": "https://schema.org",
    "@type": "WebSite",
    "name": "Kanha Fashion Hub",
    "url": "<?php echo e(url('/')); ?>",
    "description": "Discover beautiful handcrafted fashion jewelry at Kanha Fashion Hub. Premium rings, necklaces, earrings, and bracelets with free shipping across India.",
    "potentialAction": {
        "@type": "SearchAction",
        "target": {
            "@type": "EntryPoint",
            "urlTemplate": "<?php echo e(url('/collections')); ?>?search={search_term_string}"
        },
        "query-input": "required name=search_term_string"
    }
}
</script>

<script type="application/ld+json">
{
    "@context": "https://schema.org",
    "@type": "Store",
    "name": "Kanha Fashion Hub",
    "image": "<?php echo e(asset('images/store-front.jpg')); ?>",
    "description": "Premium handcrafted fashion jewelry store offering exquisite rings, necklaces, earrings, and bracelets.",
    "address": {
        "@type": "PostalAddress",
        "addressCountry": "IN"
    },
    "geo": {
        "@type": "GeoCoordinates",
        "latitude": "19.0760",
        "longitude": "72.8777"
    },
    "url": "<?php echo e(url('/')); ?>",
    "telephone": "+91-XXXXXXXXXX",
    "priceRange": "₹₹₹",
    "paymentAccepted": ["Cash", "Credit Card", "Debit Card", "UPI", "Net Banking"],
    "currenciesAccepted": "INR",
    "openingHours": "Mo-Sa 10:00-20:00"
}
</script>
<?php $__env->stopPush(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
document.addEventListener('DOMContentLoaded', function() {
    const storyVideoModal = document.getElementById('storyVideoModal');
    const storyVideo = document.getElementById('storyVideo');

    if (storyVideoModal && storyVideo) {
        // Pause video when modal is closed
        storyVideoModal.addEventListener('hidden.bs.modal', function () {
            storyVideo.pause();
            storyVideo.currentTime = 0;
        });

        // Optional: Auto-play when modal opens (be careful with browser policies)
        storyVideoModal.addEventListener('shown.bs.modal', function () {
            // Uncomment the next line if you want auto-play (may not work on all browsers/devices)
            // storyVideo.play().catch(e => console.log('Auto-play prevented:', e));
        });
    }
});
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\kanha-shop-web\resources\views/welcome.blade.php ENDPATH**/ ?>