<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * @property int $id
 * @property int $product_id
 * @property int $size_id
 * @property int $stock_quantity
 * @property string $sku_suffix
 * @property float $price_adjustment
 * @property bool $is_available
 * @property \Illuminate\Support\Carbon $created_at
 * @property \Illuminate\Support\Carbon $updated_at
 *
 * @property-read \App\Models\Product $product
 * @property-read \App\Models\Size $size
 *
 * @method static \Illuminate\Database\Eloquent\Builder available()
 * @method static \Illuminate\Database\Eloquent\Builder inStock()
 * @method static \Illuminate\Database\Eloquent\Builder forProduct(int $productId)
 * @method static \Illuminate\Database\Eloquent\Builder forSize(int $sizeId)
 */
class ProductSize extends Model
{
    use HasFactory;

    protected $fillable = [
        'product_id',
        'size_id',
        'stock_quantity',
        'sku_suffix',
        'price_adjustment',
        'is_available',
    ];

    protected $casts = [
        'price_adjustment' => 'decimal:2',
        'is_available' => 'boolean',
    ];

    // Relationships
    public function product()
    {
        return $this->belongsTo(Product::class);
    }

    public function size()
    {
        return $this->belongsTo(Size::class);
    }

    // Scopes
    public function scopeAvailable($query)
    {
        return $query->where('is_available', true);
    }

    public function scopeInStock($query)
    {
        return $query->where('stock_quantity', '>', 0);
    }

    public function scopeForProduct($query, $productId)
    {
        return $query->where('product_id', $productId);
    }

    public function scopeForSize($query, $sizeId)
    {
        return $query->where('size_id', $sizeId);
    }

    // Helper methods
    public function isInStock()
    {
        return $this->is_available && $this->stock_quantity > 0;
    }

    public function getFullSkuAttribute()
    {
        $baseSku = $this->product->sku;
        return $this->sku_suffix ? $baseSku . $this->sku_suffix : $baseSku;
    }

    public function getFinalPriceAttribute()
    {
        return $this->product->current_price + $this->price_adjustment;
    }

    /**
     * Decrease stock quantity
     */
    public function decrementStock($quantity = 1)
    {
        if ($this->stock_quantity >= $quantity) {
            $this->decrement('stock_quantity', $quantity);
            return true;
        }
        return false;
    }

    /**
     * Increase stock quantity
     */
    public function incrementStock($quantity = 1)
    {
        $this->increment('stock_quantity', $quantity);
        return true;
    }

    /**
     * Check if sufficient stock is available
     */
    public function hasSufficientStock($quantity)
    {
        return $this->is_available && $this->stock_quantity >= $quantity;
    }

    /**
     * Get stock status for display
     */
    public function getStockStatusAttribute()
    {
        if (!$this->is_available) {
            return 'unavailable';
        }
        
        if ($this->stock_quantity <= 0) {
            return 'out_of_stock';
        }
        
        if ($this->stock_quantity <= 5) {
            return 'low_stock';
        }
        
        return 'in_stock';
    }

    /**
     * Get stock status message for display
     */
    public function getStockStatusMessageAttribute()
    {
        switch ($this->stock_status) {
            case 'unavailable':
                return 'Unavailable';
            case 'out_of_stock':
                return 'Out of Stock';
            case 'low_stock':
                return "Only {$this->stock_quantity} left";
            case 'in_stock':
                return 'In Stock';
            default:
                return '';
        }
    }
}
