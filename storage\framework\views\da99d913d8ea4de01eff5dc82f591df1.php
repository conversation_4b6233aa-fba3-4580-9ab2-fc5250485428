<?php $__env->startSection('title', 'Edit Category - Admin - Kanha Fashion Hub'); ?>

<?php $__env->startSection('content'); ?>
<!-- Page Header -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h1 class="h3 mb-0 text-gray-800 font-playfair">Edit Category</h1>
        <p class="mb-0 text-muted">Update category information for: <?php echo e($category->name); ?></p>
    </div>
    <div>
        <a href="<?php echo e(route('admin.categories.index')); ?>" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-left me-2"></i>Back to Categories
        </a>
    </div>
</div>

<!-- Category Form -->
<div class="row">
    <div class="col-12 col-lg-8">
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-light">
                <h5 class="mb-0">Category Information</h5>
            </div>
            <div class="card-body">
                <form id="categoryForm" enctype="multipart/form-data">
                    <?php echo csrf_field(); ?>
                    <?php echo method_field('PUT'); ?>
                    
                    <!-- Basic Information -->
                    <div class="mb-3">
                        <label for="name" class="form-label">Category Name <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="name" name="name" value="<?php echo e($category->name); ?>" required>
                        <div class="invalid-feedback"></div>
                    </div>

                    <div class="mb-3">
                        <label for="description" class="form-label">Description</label>
                        <textarea class="form-control" id="description" name="description" rows="3" placeholder="Brief description of this category"><?php echo e($category->description); ?></textarea>
                        <div class="invalid-feedback"></div>
                    </div>

                    <!-- Parent Category -->
                    <div class="mb-3">
                        <label for="parent_id" class="form-label">Parent Category</label>
                        <select class="form-select" id="parent_id" name="parent_id">
                            <option value="">Select Parent Category (Optional)</option>
                            <?php $__currentLoopData = $parentCategories; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $parentCategory): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <option value="<?php echo e($parentCategory->id); ?>" <?php echo e($category->parent_id == $parentCategory->id ? 'selected' : ''); ?>>
                                    <?php echo e($parentCategory->name); ?>

                                </option>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </select>
                        <small class="form-text text-muted">Leave empty to make this a root category</small>
                    </div>

                    <!-- Current Image -->
                    <?php if($category->image): ?>
                    <div class="mb-3">
                        <label class="form-label">Current Image</label>
                        <div id="currentImage">
                            <div class="image-preview">
                                <img src="<?php echo e(asset('storage/' . $category->image)); ?>" alt="Current Category Image" style="width: 150px; height: 150px; object-fit: cover; border-radius: 8px;">
                                <button type="button" class="remove-current-image btn btn-sm btn-danger" style="position: absolute; top: -8px; right: -8px; border-radius: 50%; width: 24px; height: 24px; font-size: 12px;">×</button>
                            </div>
                        </div>
                    </div>
                    <?php endif; ?>

                    <!-- Category Image -->
                    <div class="mb-3">
                        <label for="image" class="form-label"><?php echo e($category->image ? 'Replace Image' : 'Category Image'); ?></label>
                        <input type="file" class="form-control" id="image" name="image" accept="image/*">
                        <small class="form-text text-muted"><?php echo e($category->image ? 'Upload a new image to replace the current one' : 'Upload an image to represent this category'); ?></small>
                        <div class="invalid-feedback"></div>
                        <div id="imagePreview" class="mt-3"></div>
                    </div>

                    <!-- Size Management -->
                    <div class="mb-4">
                        <h6 class="mb-3">Size Management</h6>

                        <!-- Size Inheritance -->
                        <div class="mb-3">
                            <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox" id="inherit_parent_sizes" name="inherit_parent_sizes" value="1"
                                       <?php echo e($category->inherit_parent_sizes ? 'checked' : ''); ?>>
                                <label class="form-check-label" for="inherit_parent_sizes">
                                    Inherit sizes from parent category
                                </label>
                            </div>
                            <small class="form-text text-muted">If enabled, this category will use sizes from its parent category</small>
                        </div>

                        <!-- Available Sizes -->
                        <div class="mb-3" id="size-selection">
                            <label class="form-label">Available Sizes</label>
                            <div class="row" id="sizes-container">
                                <?php $__currentLoopData = $sizes; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $size): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <div class="col-md-3 mb-2">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" name="size_ids[]" value="<?php echo e($size->id); ?>"
                                                   id="size_<?php echo e($size->id); ?>" <?php echo e(in_array($size->id, $categorySizeIds ?? []) ? 'checked' : ''); ?>>
                                            <label class="form-check-label" for="size_<?php echo e($size->id); ?>">
                                                <?php echo e($size->display_name); ?>

                                                <small class="text-muted">(<?php echo e(ucfirst($size->category_type)); ?>)</small>
                                            </label>
                                        </div>
                                    </div>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </div>
                            <small class="form-text text-muted">Select sizes that will be available for products in this category</small>
                        </div>
                    </div>

                    <!-- Settings -->
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="sort_order" class="form-label">Sort Order</label>
                            <input type="number" class="form-control" id="sort_order" name="sort_order" value="<?php echo e($category->sort_order); ?>" min="0">
                            <small class="form-text text-muted">Lower numbers appear first</small>
                            <div class="invalid-feedback"></div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <div class="form-check form-switch mt-4">
                                <input type="hidden" name="is_active" value="0">
                                <input class="form-check-input" type="checkbox" id="is_active" name="is_active" value="1" <?php echo e($category->is_active ? 'checked' : ''); ?>>
                                <label class="form-check-label" for="is_active">
                                    Active Category
                                </label>
                            </div>
                            <small class="form-text text-muted">Only active categories are visible to customers</small>
                        </div>
                        <div class="col-md-6 mb-3">
                            <div class="form-check form-switch mt-4">
                                <input type="hidden" name="is_featured" value="0">
                                <input class="form-check-input" type="checkbox" id="is_featured" name="is_featured" value="1" <?php echo e($category->is_featured ? 'checked' : ''); ?>>
                                <label class="form-check-label" for="is_featured">
                                    <strong>Featured Category</strong>
                                </label>
                            </div>
                            <small class="form-text text-muted">Featured categories appear in the "Featured Collections" section on homepage</small>
                        </div>
                    </div>

                    <!-- Form Actions -->
                    <div class="d-flex justify-content-end gap-2">
                        <a href="<?php echo e(route('admin.categories.index')); ?>" class="btn btn-outline-secondary">Cancel</a>
                        <button type="submit" class="btn btn-primary-pink" id="submitBtn">
                            <i class="fas fa-save me-2"></i>Update Category
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Info Panel -->
    <div class="col-12 col-lg-4">
        <div class="card border-0 shadow-sm mb-3">
            <div class="card-header bg-light">
                <h6 class="mb-0">Category Statistics</h6>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-6">
                        <div class="border-end">
                            <h4 class="text-primary mb-1"><?php echo e($category->products()->count()); ?></h4>
                            <small class="text-muted">Products</small>
                        </div>
                    </div>
                    <div class="col-6">
                        <h4 class="text-success mb-1"><?php echo e($category->products()->where('status', 'active')->count()); ?></h4>
                        <small class="text-muted">Active</small>
                    </div>
                </div>
            </div>
        </div>

        <div class="card border-0 shadow-sm">
            <div class="card-header bg-light">
                <h6 class="mb-0">Category Guidelines</h6>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <h6 class="text-primary">Naming Tips</h6>
                    <ul class="small text-muted mb-0">
                        <li>Use clear, descriptive names</li>
                        <li>Keep names concise but informative</li>
                        <li>Consider customer search terms</li>
                    </ul>
                </div>

                <div class="mb-3">
                    <h6 class="text-primary">Image Guidelines</h6>
                    <ul class="small text-muted mb-0">
                        <li>Recommended size: 400x400px</li>
                        <li>Use high-quality images</li>
                        <li>Square aspect ratio works best</li>
                        <li>File formats: JPG, PNG, GIF</li>
                    </ul>
                </div>

                <div>
                    <h6 class="text-primary">Organization</h6>
                    <ul class="small text-muted mb-0">
                        <li>Use sort order to control display sequence</li>
                        <li>Group related products logically</li>
                        <li>Consider creating subcategories for large collections</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('styles'); ?>
<style>
    .image-preview {
        position: relative;
        display: inline-block;
        margin: 5px 0;
    }
    
    .image-preview img {
        width: 150px;
        height: 150px;
        object-fit: cover;
        border-radius: 8px;
        border: 2px solid #dee2e6;
    }
    
    .image-preview .remove-image,
    .image-preview .remove-current-image {
        position: absolute;
        top: -8px;
        right: -8px;
        background: #dc3545;
        color: white;
        border: none;
        border-radius: 50%;
        width: 24px;
        height: 24px;
        font-size: 12px;
        cursor: pointer;
    }
</style>
<?php $__env->stopPush(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('categoryForm');
    const submitBtn = document.getElementById('submitBtn');
    const imageInput = document.getElementById('image');
    const imagePreview = document.getElementById('imagePreview');
    
    // Image preview functionality
    imageInput.addEventListener('change', function(e) {
        imagePreview.innerHTML = '';
        const file = e.target.files[0];
        
        if (file && file.type.startsWith('image/')) {
            const reader = new FileReader();
            reader.onload = function(e) {
                imagePreview.innerHTML = `
                    <div class="image-preview">
                        <img src="${e.target.result}" alt="Preview">
                        <button type="button" class="remove-image">×</button>
                    </div>
                `;
            };
            reader.readAsDataURL(file);
        }
    });
    
    // Remove new image functionality
    imagePreview.addEventListener('click', function(e) {
        if (e.target.classList.contains('remove-image')) {
            imageInput.value = '';
            imagePreview.innerHTML = '';
        }
    });
    
    // Remove current image functionality
    document.addEventListener('click', function(e) {
        if (e.target.classList.contains('remove-current-image')) {
            e.target.closest('#currentImage').remove();
        }
    });
    
    // Form submission
    form.addEventListener('submit', function(e) {
        e.preventDefault();
        
        submitBtn.disabled = true;
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Updating...';
        
        // Clear previous errors
        document.querySelectorAll('.is-invalid').forEach(el => el.classList.remove('is-invalid'));
        document.querySelectorAll('.invalid-feedback').forEach(el => el.textContent = '');
        
        const formData = new FormData(form);
        
        fetch('<?php echo e(route("admin.categories.update", $category->id)); ?>', {
            method: 'POST',
            body: formData,
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            }
        })
        .then(async response => {
            const data = await response.json();

            if (response.ok && data.success) {
                // Show success message
                const alert = document.createElement('div');
                alert.className = 'alert alert-success alert-dismissible fade show';
                alert.innerHTML = `
                    ${data.message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                `;
                form.insertBefore(alert, form.firstChild);

                // Scroll to top to show message
                window.scrollTo(0, 0);
                return;
            }

            // Handle errors
            let errorMessage = 'An error occurred while updating the category.';
            let validationErrors = null;

            if (response.status === 422) {
                // Validation errors
                errorMessage = data.message || 'Please check the form for errors.';
                validationErrors = data.errors;
            } else if (response.status === 400) {
                // InvalidArgumentException or other client errors
                errorMessage = data.message || errorMessage;
            } else if (data.message) {
                errorMessage = data.message;
            }

            // Show error message
            const alert = document.createElement('div');
            alert.className = 'alert alert-danger alert-dismissible fade show';
            alert.innerHTML = `
                <strong>Error:</strong> ${errorMessage}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;
            form.insertBefore(alert, form.firstChild);

            // Handle validation errors
            if (validationErrors) {
                Object.keys(validationErrors).forEach(field => {
                    const input = document.querySelector(`[name="${field}"]`);
                    if (input) {
                        input.classList.add('is-invalid');
                        const feedback = input.parentNode.querySelector('.invalid-feedback');
                        if (feedback) {
                            feedback.textContent = validationErrors[field][0];
                        }
                    }
                });

                // Show detailed validation errors in alert
                const errorList = Object.keys(validationErrors).map(field =>
                    `• ${field}: ${validationErrors[field][0]}`
                ).join('<br>');

                const detailAlert = document.createElement('div');
                detailAlert.className = 'alert alert-warning alert-dismissible fade show mt-2';
                detailAlert.innerHTML = `
                    <strong>Validation Errors:</strong><br>
                    ${errorList}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                `;
                form.insertBefore(detailAlert, form.children[1]);
            }

            // Scroll to top to show message
            window.scrollTo(0, 0);
        })
        .catch(error => {
            console.error('Network or parsing error:', error);

            // Show network error message
            const alert = document.createElement('div');
            alert.className = 'alert alert-danger alert-dismissible fade show';
            alert.innerHTML = `
                <strong>Network Error:</strong> Unable to connect to server. Please check your connection and try again.
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;
            form.insertBefore(alert, form.firstChild);

            // Scroll to top to show message
            window.scrollTo(0, 0);
        })
        .finally(() => {
            submitBtn.disabled = false;
            submitBtn.innerHTML = '<i class="fas fa-save me-2"></i>Update Category';
        });

        // Handle parent category change for size inheritance
        const parentSelect = document.getElementById('parent_id');
        const inheritCheckbox = document.getElementById('inherit_parent_sizes');
        const sizeSelection = document.getElementById('size-selection');

        function toggleSizeSelection() {
            if (inheritCheckbox.checked && parentSelect.value) {
                sizeSelection.style.opacity = '0.5';
                sizeSelection.style.pointerEvents = 'none';
            } else {
                sizeSelection.style.opacity = '1';
                sizeSelection.style.pointerEvents = 'auto';
            }
        }

        parentSelect.addEventListener('change', function() {
            if (this.value) {
                // Load parent category sizes
                fetch(`/admin/api/categories/${this.value}/sizes`)
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            // Show inherited sizes info
                            const inheritedInfo = document.createElement('div');
                            inheritedInfo.className = 'alert alert-info mt-2';
                            inheritedInfo.innerHTML = `
                                <small><strong>Parent category sizes:</strong> ${data.sizes.map(s => s.display_name).join(', ')}</small>
                            `;

                            // Remove existing inherited info
                            const existingInfo = sizeSelection.querySelector('.alert-info');
                            if (existingInfo) {
                                existingInfo.remove();
                            }

                            sizeSelection.appendChild(inheritedInfo);
                        }
                    })
                    .catch(error => console.error('Error loading parent sizes:', error));
            } else {
                // Remove inherited sizes info
                const existingInfo = sizeSelection.querySelector('.alert-info');
                if (existingInfo) {
                    existingInfo.remove();
                }
            }
            toggleSizeSelection();
        });

        inheritCheckbox.addEventListener('change', toggleSizeSelection);

        // Initial toggle
        toggleSizeSelection();
    });
});
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.admin', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\kanha-shop-web\resources\views/admin/categories/edit.blade.php ENDPATH**/ ?>