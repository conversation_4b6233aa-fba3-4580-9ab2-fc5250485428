<?php

namespace App\Http\Controllers;

use App\Models\Order;
use App\Models\OrderItem;
use App\Models\CartItem;
use App\Models\User;
use App\Services\TwilioService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class OrderController extends Controller
{
    protected $twilioService;

    public function __construct(TwilioService $twilioService)
    {
        $this->twilioService = $twilioService;
        // No authentication required for guest-only experience
    }

    public function index()
    {
        $orders = Auth::user()->orders()
            ->with('orderItems.product')
            ->recent()
            ->paginate(10);

        return view('user.orders', compact('orders'));
    }

    public function show($id)
    {
        $order = Auth::user()->orders()
            ->with('orderItems.product')
            ->findOrFail($id);

        return view('user.order-detail', compact('order'));
    }

    public function checkout()
    {
        // Get cart items from session (guest-only experience)
        $sessionId = session()->getId();
        $cartItems = CartItem::getCartItems(null, $sessionId);

        if ($cartItems->isEmpty()) {
            return redirect()->route('cart')->with('error', 'Your cart is empty.');
        }

        // Always proceed with guest checkout
        $cartSummary = CartItem::getCartSummary(null, $sessionId);

        // Apply promo code discount if available
        $appliedPromo = session('applied_promocode');
        if ($appliedPromo && isset($appliedPromo['discount']) && is_numeric($appliedPromo['discount'])) {
            $promoDiscount = floatval($appliedPromo['discount']);

            // Ensure promo discount is not negative
            if ($promoDiscount > 0) {
                // Recalculate totals with promo discount
                $subtotalAfterDiscount = $cartSummary['subtotal'] - $cartSummary['discount'];
                $taxableAmount = max(0, $subtotalAfterDiscount - $promoDiscount);
                $tax = $taxableAmount * 0.03;

                // Calculate final total ensuring it's never negative
                $finalTotal = $subtotalAfterDiscount - $promoDiscount + $cartSummary['shipping'] + $tax;
                $total = max(0, $finalTotal);

                // Update cart summary with promo-adjusted values
                $cartSummary['tax'] = round($tax, 2);
                $cartSummary['total'] = round($total, 2);
                $cartSummary['promo_discount'] = $promoDiscount;
                $cartSummary['final_subtotal'] = $subtotalAfterDiscount;
            }
        }

        return view('checkout.index', compact('cartItems', 'cartSummary'));
    }

    /**
     * Guest checkout - prompt for mobile number and OTP
     */
    public function guestCheckout()
    {
        $userId = null;
        $sessionId = session()->getId();
        $cartSummary = CartItem::getCartSummary($userId, $sessionId);

        if ($cartSummary['items']->isEmpty()) {
            return redirect()->route('cart')->with('error', 'Your cart is empty.');
        }

        return view('checkout.guest', compact('cartSummary'));
    }

    public function store(Request $request)
    {
        $request->validate([
            'billing_address' => 'required|array',
            'billing_address.name' => 'required|string|max:255',
            'billing_address.email' => 'required|email',
            'billing_address.phone' => 'required|string',
            'billing_address.address' => 'required|string',
            'billing_address.city' => 'required|string',
            'billing_address.state' => 'required|string',
            'billing_address.pincode' => 'required|string',
            'shipping_address' => 'required|array',
            'shipping_method' => 'required|string',
            'payment_method' => 'required|string',
            'notes' => 'nullable|string'
        ]);

        // Always work with guest session (no authentication required)
        $sessionId = session()->getId();

        // Get cart items from session
        $cartItems = CartItem::getCartItems(null, $sessionId);

        if ($cartItems->isEmpty()) {
            return response()->json([
                'success' => false,
                'message' => 'Your cart is empty.'
            ], 400);
        }

        // Create or find user by phone for order tracking
        $phone = preg_replace('/[^0-9]/', '', $request->billing_address['phone']);
        $user = User::firstOrCreate(
            ['phone' => $phone],
            [
                'name' => $request->billing_address['name'],
                'email' => $request->billing_address['email'],
                'phone_verified_at' => now(), // Auto-verify for seamless experience
                'role' => 'customer',
            ]
        );

        // Update user info if it has changed
        $user->update([
            'name' => $request->billing_address['name'],
            'email' => $request->billing_address['email'],
        ]);

        DB::beginTransaction();

        try {
            // Calculate totals using consistent logic with cart
            $cartSummary = CartItem::getCartSummary(null, session()->getId());

            // Apply any promocode discount
            $promoDiscount = 0;
            $appliedPromo = session('applied_promocode');
            if ($appliedPromo) {
                $promoDiscount = $appliedPromo['discount'];
            }

            $subtotal = $cartSummary['subtotal'];
            $discountAmount = $cartSummary['discount'] + $promoDiscount;
            $shippingAmount = $this->calculateShipping($request->shipping_method, $subtotal);
            $taxableAmount = max(0, $subtotal - $discountAmount); // Ensure taxable amount is never negative
            $taxAmount = $taxableAmount * 0.03; // 3% GST to match cart calculations
            $totalAmount = max(0, $subtotal - $discountAmount + $shippingAmount + $taxAmount); // Ensure total is never negative

            // Create order
            $order = Order::create([
                'order_number' => Order::generateOrderNumber(),
                'user_id' => $user->id,
                'status' => 'pending',
                'subtotal' => $subtotal,
                'tax_amount' => $taxAmount,
                'shipping_amount' => $shippingAmount,
                'discount_amount' => $discountAmount,
                'total_amount' => $totalAmount,
                'currency' => 'INR',
                'payment_status' => 'pending',
                'payment_method' => $request->payment_method,
                'billing_address' => $request->billing_address,
                'shipping_address' => $request->shipping_address,
                'shipping_method' => $request->shipping_method,
                'notes' => $request->notes
            ]);

            // Create order items and update stock
            foreach ($cartItems as $cartItem) {
                $product = $cartItem->product;
                $sizeId = $cartItem->size_id;
                $sizeName = $cartItem->getSizeNameAttribute();

                // Check stock availability for specific size or overall product
                if ($sizeId) {
                    $productSize = $product->getProductSize($sizeId);
                    if (!$productSize || !$productSize->hasSufficientStock($cartItem->quantity)) {
                        throw new \Exception("Insufficient stock for product: {$product->name} (Size: {$sizeName})");
                    }
                } else {
                    if (!$product->hasSufficientStock($cartItem->quantity)) {
                        throw new \Exception("Insufficient stock for product: {$product->name}");
                    }
                }

                // Get the actual price (including size adjustments)
                $actualPrice = $cartItem->getTotalPriceAttribute() / $cartItem->quantity;

                // Create order item
                OrderItem::create([
                    'order_id' => $order->id,
                    'product_id' => $product->id,
                    'product_name' => $product->name,
                    'product_sku' => $product->sku,
                    'price' => $actualPrice,
                    'quantity' => $cartItem->quantity,
                    'size_id' => $sizeId,
                    'size_name' => $sizeName, // Store size name for historical record
                    'size' => $cartItem->size, // Keep old field for backward compatibility
                    'product_options' => $cartItem->options,
                    'total' => $actualPrice * $cartItem->quantity
                ]);

                // Update product stock for specific size or overall product
                if ($sizeId) {
                    $productSize = $product->getProductSize($sizeId);
                    $productSize->decrementStock($cartItem->quantity);
                } else {
                    $product->decrementStock($cartItem->quantity);
                }
            }

            DB::commit();

            // Only handle Razorpay payments - COD removed for better user experience
            if ($request->payment_method === 'razorpay') {
                // For Razorpay, don't clear cart yet - wait for payment confirmation
                return response()->json([
                    'success' => true,
                    'message' => 'Order created successfully! Redirecting to secure payment...',
                    'order_id' => $order->id,
                    'payment_method' => 'razorpay',
                    'requires_payment' => true
                ]);
            } else {
                // Only Razorpay is supported
                return response()->json([
                    'success' => false,
                    'message' => 'Invalid payment method. Only secure online payment is supported.'
                ], 400);
            }

        } catch (\Exception $e) {
            DB::rollback();

            return response()->json([
                'success' => false,
                'message' => 'Order creation failed: ' . $e->getMessage()
            ], 500);
        }
    }

    public function success($orderId)
    {
        // Public access to order success page for guest experience
        $order = Order::with(['orderItems.product', 'user'])
            ->findOrFail($orderId);

        return view('checkout.success', compact('order'));
    }



    public function cancel($id)
    {
        $order = Auth::user()->orders()->findOrFail($id);

        if (!$order->canBeCancelled()) {
            return response()->json([
                'success' => false,
                'message' => 'This order cannot be cancelled.'
            ], 400);
        }

        DB::beginTransaction();

        try {
            // Restore stock
            foreach ($order->orderItems as $orderItem) {
                $product = $orderItem->product;
                if ($product) {
                    $product->incrementStock($orderItem->quantity);
                }
            }

            // Update order status
            $order->update(['status' => 'cancelled']);

            // Update user stats
            $order->user->decrement('total_spent', $order->total_amount);

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'Order cancelled successfully!'
            ]);

        } catch (\Exception $e) {
            DB::rollback();

            return response()->json([
                'success' => false,
                'message' => 'Failed to cancel order: ' . $e->getMessage()
            ], 500);
        }
    }

    private function calculateShipping($method, $subtotal)
    {
        // Get dynamic shipping settings
        $freeShippingThreshold = \App\Models\Setting::get('free_shipping_threshold', 25000);
        $standardShipping = \App\Models\Setting::get('shipping_charge', 500);
        $expressShipping = \App\Models\Setting::get('express_shipping_charge', 1000);

        // Base shipping cost (free shipping above threshold)
        $baseShipping = $subtotal >= $freeShippingThreshold ? 0 : $standardShipping;

        switch ($method) {
            case 'standard':
                return $baseShipping;
            case 'express':
                return $subtotal >= $freeShippingThreshold ? $expressShipping : $baseShipping + $expressShipping;
            case 'overnight':
                return $subtotal >= $freeShippingThreshold ? 1500 : $baseShipping + 1500; // Additional ₹1500 for overnight
            default:
                return $baseShipping;
        }
    }

    private function processPayment($order, $paymentMethod)
    {
        // Mock payment processing
        // In real implementation, integrate with payment gateways like Razorpay, Stripe, etc.

        $success = true; // Simulate successful payment
        $transactionId = 'TXN_' . time() . '_' . $order->id;

        return [
            'success' => $success,
            'transaction_id' => $transactionId,
            'message' => $success ? 'Payment successful' : 'Payment failed'
        ];
    }
}
