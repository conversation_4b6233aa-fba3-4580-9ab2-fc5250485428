<?php $__env->startSection('title', 'Manage Orders - Admin - Kanha Fashion Hub'); ?>

<?php $__env->startSection('content'); ?>
<!-- Page Header -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h1 class="h3 mb-0 text-gray-800 font-playfair">Order Management</h1>
        <p class="mb-0 text-muted">Manage customer orders and fulfillment</p>
    </div>
    <div>
        <a href="<?php echo e(route('admin.orders.tracking.index')); ?>" class="btn btn-primary-pink">
            <i class="fas fa-truck me-2"></i>Order Tracking
        </a>
    </div>
</div>

<!-- Quick Stats -->
<div class="row g-4 mb-4">
    <div class="col-lg-3 col-md-6">
        <div class="card border-0 shadow-sm">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="bg-primary text-white rounded-circle d-flex align-items-center justify-content-center me-3"
                         style="width: 50px; height: 50px;">
                        <i class="fas fa-shopping-cart"></i>
                    </div>
                    <div>
                        <h4 class="mb-0"><?php echo e($totalOrders); ?></h4>
                        <p class="text-muted mb-0">Total Orders</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-lg-3 col-md-6">
        <div class="card border-0 shadow-sm">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="bg-warning text-white rounded-circle d-flex align-items-center justify-content-center me-3"
                         style="width: 50px; height: 50px;">
                        <i class="fas fa-clock"></i>
                    </div>
                    <div>
                        <h4 class="mb-0"><?php echo e($pendingOrders); ?></h4>
                        <p class="text-muted mb-0">Pending</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-lg-3 col-md-6">
        <div class="card border-0 shadow-sm">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="bg-info text-white rounded-circle d-flex align-items-center justify-content-center me-3"
                         style="width: 50px; height: 50px;">
                        <i class="fas fa-truck"></i>
                    </div>
                    <div>
                        <h4 class="mb-0"><?php echo e($shippedOrders); ?></h4>
                        <p class="text-muted mb-0">Shipped</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-lg-3 col-md-6">
        <div class="card border-0 shadow-sm">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="bg-success text-white rounded-circle d-flex align-items-center justify-content-center me-3"
                         style="width: 50px; height: 50px;">
                        <i class="fas fa-check-circle"></i>
                    </div>
                    <div>
                        <h4 class="mb-0"><?php echo e($deliveredOrders); ?></h4>
                        <p class="text-muted mb-0">Delivered</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Filters -->
<div class="card border-0 shadow-sm mb-4">
    <div class="card-body">
        <form method="GET" action="<?php echo e(route('admin.orders.index')); ?>">
            <div class="row g-3 align-items-center">
                <div class="col-md-3">
                    <div class="input-group">
                        <input type="text" class="form-control" name="search"
                               placeholder="Search orders..." value="<?php echo e(request('search')); ?>">
                        <button class="btn btn-outline-secondary" type="submit">
                            <i class="fas fa-search"></i>
                        </button>
                    </div>
                </div>
                <div class="col-md-2">
                    <select class="form-select" name="status">
                        <option value="">All Status</option>
                        <option value="pending" <?php echo e(request('status') == 'pending' ? 'selected' : ''); ?>>Pending</option>
                        <option value="confirmed" <?php echo e(request('status') == 'confirmed' ? 'selected' : ''); ?>>Confirmed</option>
                        <option value="processing" <?php echo e(request('status') == 'processing' ? 'selected' : ''); ?>>Processing</option>
                        <option value="shipped" <?php echo e(request('status') == 'shipped' ? 'selected' : ''); ?>>Shipped</option>
                        <option value="delivered" <?php echo e(request('status') == 'delivered' ? 'selected' : ''); ?>>Delivered</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <input type="date" class="form-control" name="date_from"
                           placeholder="From Date" value="<?php echo e(request('date_from')); ?>">
                </div>
                <div class="col-md-2">
                    <input type="date" class="form-control" name="date_to"
                           placeholder="To Date" value="<?php echo e(request('date_to')); ?>">
                </div>
                <div class="col-md-2">
                    <button class="btn btn-outline-primary w-100" type="submit">
                        <i class="fas fa-filter me-1"></i>Filter
                    </button>
                </div>
                <div class="col-md-1">
                    <a href="<?php echo e(route('admin.orders.export', request()->query())); ?>"
                       class="btn btn-outline-success w-100" title="Export">
                        <i class="fas fa-download"></i>
                    </a>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- Orders Table -->
<div class="card border-0 shadow-sm">
    <div class="card-header bg-light d-flex justify-content-between align-items-center">
        <h5 class="mb-0">Recent Orders</h5>
        <div class="d-flex gap-2">
            <button class="btn btn-outline-secondary btn-sm">
                <i class="fas fa-download me-1"></i>Export
            </button>
            <button class="btn btn-outline-primary btn-sm">
                <i class="fas fa-sync me-1"></i>Refresh
            </button>
        </div>
    </div>
    <div class="card-body p-0">
        <div class="table-responsive">
            <table class="table table-hover mb-0">
                <thead class="table-light">
                    <tr>
                        <th>Order #</th>
                        <th>Customer</th>
                        <th>Items</th>
                        <th>Total</th>
                        <th>Status</th>
                        <th>Payment</th>
                        <th>Date</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <?php $__empty_1 = true; $__currentLoopData = $orders; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $order): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                    <tr>
                        <td><strong><?php echo e($order->order_number); ?></strong></td>
                        <td>
                            <div class="d-flex align-items-center">
                                <div class="bg-primary-pink text-white rounded-circle d-flex align-items-center justify-content-center me-2"
                                     style="width: 30px; height: 30px; font-size: 12px;">
                                    <?php echo e(strtoupper(substr($order->user->name ?? 'G', 0, 1))); ?>

                                </div>
                                <div>
                                    <div><?php echo e($order->user->name ?? 'Guest'); ?></div>
                                    <small class="text-muted"><?php echo e($order->user->email ?? 'N/A'); ?></small>
                                </div>
                            </div>
                        </td>
                        <td><?php echo e($order->orderItems->count()); ?> items</td>
                        <td><strong>₹<?php echo e(number_format($order->total_amount, 0)); ?></strong></td>
                        <td>
                            <span class="badge bg-<?php echo e($order->status == 'pending' ? 'warning' :
                                ($order->status == 'delivered' ? 'success' :
                                ($order->status == 'cancelled' ? 'danger' : 'info'))); ?>"><?php echo e(ucfirst($order->status)); ?></span>
                        </td>
                        <td>
                            <span class="badge bg-<?php echo e($order->payment_status == 'completed' ? 'success' : 'warning'); ?>">
                                <?php echo e(ucfirst($order->payment_status ?? 'pending')); ?>

                            </span>
                        </td>
                        <td><?php echo e($order->created_at->format('M d, Y')); ?></td>
                        <td>
                            <div class="btn-group" role="group">
                                <a href="<?php echo e(route('admin.orders.show', $order->id)); ?>"
                                   class="btn btn-outline-primary btn-sm" title="View Details">
                                    <i class="fas fa-eye"></i>
                                </a>
                                <button class="btn btn-outline-success btn-sm"
                                        title="Update Status"
                                        onclick="showStatusModal(<?php echo e($order->id); ?>, '<?php echo e($order->status); ?>')">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <a href="<?php echo e(route('admin.orders.tracking.show', $order->id)); ?>"
                                   class="btn btn-outline-info btn-sm" title="Tracking">
                                    <i class="fas fa-truck"></i>
                                </a>
                            </div>
                        </td>
                    </tr>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                    <tr>
                        <td colspan="8" class="text-center py-4">
                            <i class="fas fa-inbox text-muted me-2"></i>
                            <span class="text-muted">No orders found.</span>
                        </td>
                    </tr>
                    <?php endif; ?>
                </tbody>
            </table>
        </div>
    </div>
    
    <!-- Pagination -->
    <div class="card-footer bg-light">
        <div class="row align-items-center">
            <div class="col-md-6">
                <p class="text-muted mb-0">
                    Showing <?php echo e($orders->firstItem() ?? 0); ?> to <?php echo e($orders->lastItem() ?? 0); ?> of <?php echo e($orders->total()); ?> orders
                </p>
            </div>
            <div class="col-md-6">
                <?php echo e($orders->appends(request()->query())->links()); ?>

            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.admin', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\kanha-shop-web\resources\views/admin/orders/index.blade.php ENDPATH**/ ?>