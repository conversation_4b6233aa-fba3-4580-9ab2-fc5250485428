<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Product;
use App\Models\ProductSize;
use App\Models\Category;
use App\Models\Size;
use App\Models\Order;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;

class AdminController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
        $this->middleware(function ($request, $next) {
            if (!Auth::user()->isAdmin()) {
                abort(403, 'Access denied. Admin privileges required.');
            }
            return $next($request);
        });
    }

    public function dashboard()
    {
        // Get statistics
        $totalProducts = Product::count();
        $activeProducts = Product::active()->count();
        $lowStockProducts = Product::where('stock_quantity', '<=', 5)->count();
        $outOfStockProducts = Product::where('in_stock', false)->count();

        $totalOrders = Order::count();
        $pendingOrders = Order::where('status', 'pending')->count();
        $todayOrders = Order::whereDate('created_at', today())->count();
        $todayRevenue = Order::whereDate('created_at', today())->sum('total_amount');

        $totalCustomers = User::where('role', 'customer')->count();
        $newCustomers = User::where('role', 'customer')->whereDate('created_at', today())->count();

        // Get recent orders
        $recentOrders = Order::with('user', 'orderItems.product')
            ->recent()
            ->limit(10)
            ->get();

        // Get top selling products
        $topProducts = Product::withCount(['orderItems' => function($query) {
            $query->whereHas('order', function($q) {
                $q->whereMonth('created_at', now()->month);
            });
        }])
        ->orderBy('order_items_count', 'desc')
        ->limit(5)
        ->get();

        return view('admin.dashboard', compact(
            'totalProducts', 'activeProducts', 'lowStockProducts', 'outOfStockProducts',
            'totalOrders', 'pendingOrders', 'todayOrders', 'todayRevenue',
            'totalCustomers', 'newCustomers', 'recentOrders', 'topProducts'
        ));
    }

    // Product Management
    public function products()
    {
        $products = Product::with('category')
            ->orderBy('created_at', 'desc')
            ->paginate(20);

        // Get product statistics
        $totalProducts = Product::count();
        $activeProducts = Product::active()->count();
        $lowStockProducts = Product::where('stock_quantity', '<=', 5)->count();
        $outOfStockProducts = Product::where('in_stock', false)->count();

        return view('admin.products.index', compact(
            'products', 'totalProducts', 'activeProducts', 'lowStockProducts', 'outOfStockProducts'
        ));
    }

    public function createProduct()
    {
        $categories = Category::active()->ordered()->get();

        if ($categories->isEmpty()) {
            throw new \InvalidArgumentException('No active categories available. Please create and activate at least one category before adding products.');
        }

        return view('admin.products.create', compact('categories'));
    }

    public function storeProduct(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'category_id' => 'required|exists:categories,id',
                'name' => 'required|string|max:255',
                'sku' => 'required|string|unique:products,sku',
                'description' => 'required|string',
                'short_description' => 'nullable|string',
                'price' => 'required|numeric|min:0',
                'sale_price' => 'nullable|numeric|min:0|lt:price',
                'stock_quantity' => 'required|integer|min:0',
                'weight' => 'nullable|numeric|min:0',
                'metal_type' => 'nullable|string',
                'metal_purity' => 'nullable|string',
                'stone_type' => 'nullable|string',
                'stone_weight' => 'nullable|numeric|min:0',
                'certification' => 'nullable|string',
                'images.*' => 'image|mimes:jpeg,png,jpg,gif|max:2048',
                'specifications' => 'nullable',
                'sizes' => 'nullable',
                'product_sizes' => 'nullable|array',
                'product_sizes.*.size_id' => 'required|exists:sizes,id',
                'product_sizes.*.stock_quantity' => 'required|integer|min:0',
                'product_sizes.*.price_adjustment' => 'nullable|numeric',
                'product_sizes.*.sku_suffix' => 'nullable|string|max:10'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            // Validate category exists and is active
            $category = Category::find($request->category_id);
            if (!$category) {
                throw new \InvalidArgumentException('Selected category does not exist.');
            }
            if (!$category->is_active) {
                throw new \InvalidArgumentException('Cannot add products to inactive category. Please activate the category first.');
            }

            // Validate sale price logic
            if ($request->sale_price && $request->sale_price >= $request->price) {
                throw new \InvalidArgumentException('Sale price must be less than regular price.');
            }

            $data = $request->except(['images']);
            $data['slug'] = Str::slug($request->name);
            $data['status'] = 'active';
            $data['in_stock'] = $request->stock_quantity > 0;
            $data['is_featured'] = $request->has('is_featured');

            // Process sizes and specifications
            if ($request->has('sizes') && is_array($request->sizes)) {
                $data['sizes'] = array_values(array_filter($request->sizes));
            } elseif ($request->has('sizes') && is_string($request->sizes)) {
                $data['sizes'] = array_values(array_filter(explode(',', $request->sizes)));
            }

            if ($request->has('specifications') && is_array($request->specifications)) {
                $data['specifications'] = $request->specifications;
            } elseif ($request->has('specifications') && is_string($request->specifications)) {
                try {
                    $data['specifications'] = json_decode($request->specifications, true) ?: ['description' => $request->specifications];
                } catch (\Exception $e) {
                    $data['specifications'] = ['description' => $request->specifications];
                }
            }

            // Handle image uploads
            $images = [];
            if ($request->hasFile('images')) {
                foreach ($request->file('images') as $image) {
                    if (!$image->isValid()) {
                        throw new \InvalidArgumentException('One or more uploaded images are invalid.');
                    }
                    $path = $image->store('products', 'public');
                    // Ensure forward slashes are not escaped
                    $images[] = str_replace('\\', '/', $path);
                }
            }
            $data['images'] = $images;

            // Create the product
            $product = Product::create($data);

            // Handle product sizes if provided
            if ($request->has('product_sizes') && is_array($request->product_sizes)) {
                $processedSizes = []; // Track processed sizes to avoid duplicates

                foreach ($request->product_sizes as $sizeData) {
                    // Skip if size_id is empty or invalid
                    if (empty($sizeData['size_id']) || !is_numeric($sizeData['size_id'])) {
                        continue;
                    }

                    // Skip if we've already processed this size
                    if (in_array($sizeData['size_id'], $processedSizes)) {
                        continue;
                    }

                    ProductSize::create([
                        'product_id' => $product->id,
                        'size_id' => $sizeData['size_id'],
                        'stock_quantity' => $sizeData['stock_quantity'] ?? 0,
                        'price_adjustment' => $sizeData['price_adjustment'] ?? 0,
                        'sku_suffix' => $sizeData['sku_suffix'] ?? null,
                        'is_available' => isset($sizeData['is_available']) ? (bool)$sizeData['is_available'] : true,
                    ]);

                    $processedSizes[] = $sizeData['size_id'];
                }

                // Update overall stock status
                $product->updateOverallStockStatus();
            } else {
                // If no sizes provided, create a default "One Size" entry
                $oneSize = Size::where('name', 'OS')->where('category_type', 'general')->first();
                if ($oneSize) {
                    ProductSize::create([
                        'product_id' => $product->id,
                        'size_id' => $oneSize->id,
                        'stock_quantity' => $data['stock_quantity'] ?? 0,
                        'is_available' => true,
                    ]);
                }
            }

            return response()->json([
                'success' => true,
                'message' => 'Product created successfully!',
                'redirect_url' => route('admin.products.index')
            ]);
        } catch (\InvalidArgumentException $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ], 400);
        } catch (\Exception $e) {
            Log::error('Product creation failed: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'An unexpected error occurred while creating the product.'
            ], 500);
        }
    }

    public function editProduct($id)
    {
        try {
            $product = Product::with(['productSizes.size', 'category'])->findOrFail($id);
            $categories = Category::active()->ordered()->get();

            if ($categories->isEmpty()) {
                throw new \InvalidArgumentException('No active categories available. Please create and activate at least one category before editing products.');
            }

            // Get available sizes for the product's category
            $availableSizes = $product->category->getAvailableSizes();

            return view('admin.products.edit', compact('product', 'categories', 'availableSizes'));
        } catch (\Illuminate\Database\Eloquent\ModelNotFoundException $e) {
            throw new \InvalidArgumentException('Product not found.');
        }
    }

    public function updateProduct(Request $request, $id)
    {
        try {
            $product = Product::findOrFail($id);

            $validator = Validator::make($request->all(), [
                'category_id' => 'required|exists:categories,id',
                'name' => 'required|string|max:255',
                'sku' => 'required|string|unique:products,sku,' . $id,
                'description' => 'required|string',
                'short_description' => 'nullable|string',
                'price' => 'required|numeric|min:0',
                'sale_price' => 'nullable|numeric|min:0|lt:price',
                'stock_quantity' => 'required|integer|min:0',
                'weight' => 'nullable|numeric|min:0',
                'metal_type' => 'nullable|string',
                'metal_purity' => 'nullable|string',
                'stone_type' => 'nullable|string',
                'stone_weight' => 'nullable|numeric|min:0',
                'certification' => 'nullable|string',
                'images.*' => 'image|mimes:jpeg,png,jpg,gif|max:2048',
                'specifications' => 'nullable',
                'sizes' => 'nullable',
                'product_sizes' => 'nullable|array',
                'product_sizes.*.size_id' => 'required|exists:sizes,id',
                'product_sizes.*.stock_quantity' => 'required|integer|min:0',
                'product_sizes.*.price_adjustment' => 'nullable|numeric',
                'product_sizes.*.sku_suffix' => 'nullable|string|max:10'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            // Validate category exists and is active
            $category = Category::find($request->category_id);
            if (!$category) {
                throw new \InvalidArgumentException('Selected category does not exist.');
            }
            if (!$category->is_active) {
                throw new \InvalidArgumentException('Cannot assign products to inactive category. Please activate the category first.');
            }

            // Validate sale price logic
            if ($request->sale_price && $request->sale_price >= $request->price) {
                throw new \InvalidArgumentException('Sale price must be less than regular price.');
            }

            $data = $request->except(['images']);
            $data['slug'] = Str::slug($request->name);
            $data['in_stock'] = $request->stock_quantity > 0;
            $data['is_featured'] = $request->has('is_featured');

            // Process sizes and specifications
            if ($request->has('sizes') && is_array($request->sizes)) {
                $data['sizes'] = array_values(array_filter($request->sizes));
            } elseif ($request->has('sizes') && is_string($request->sizes)) {
                $data['sizes'] = array_values(array_filter(explode(',', $request->sizes)));
            }

            if ($request->has('specifications') && is_array($request->specifications)) {
                $data['specifications'] = $request->specifications;
            } elseif ($request->has('specifications') && is_string($request->specifications)) {
                try {
                    $data['specifications'] = json_decode($request->specifications, true) ?: ['description' => $request->specifications];
                } catch (\Exception $e) {
                    $data['specifications'] = ['description' => $request->specifications];
                }
            }

            // Handle image uploads
            if ($request->hasFile('images')) {
                // Validate uploaded images
                foreach ($request->file('images') as $image) {
                    if (!$image->isValid()) {
                        throw new \InvalidArgumentException('One or more uploaded images are invalid.');
                    }
                }

                // Delete old images
                if ($product->images) {
                    foreach ($product->images as $oldImage) {
                        Storage::disk('public')->delete($oldImage);
                    }
                }

                $images = [];
                foreach ($request->file('images') as $image) {
                    $path = $image->store('products', 'public');
                    // Ensure forward slashes are not escaped
                    $images[] = str_replace('\\', '/', $path);
                }
                $data['images'] = $images;
            }

            $product->update($data);

            // Handle product sizes if provided
            if ($request->has('product_sizes') && is_array($request->product_sizes)) {
                // Use database transaction for atomic operation
                DB::transaction(function () use ($product, $request) {
                    // Delete existing product sizes
                    $product->productSizes()->delete();

                    // Create new product sizes
                    foreach ($request->product_sizes as $sizeData) {
                        // Skip if size_id is empty or invalid
                        if (empty($sizeData['size_id']) || !is_numeric($sizeData['size_id'])) {
                            continue;
                        }

                        // Check if this size already exists for this product (safety check)
                        $existingSize = ProductSize::where('product_id', $product->id)
                                                  ->where('size_id', $sizeData['size_id'])
                                                  ->first();

                        if (!$existingSize) {
                            ProductSize::create([
                                'product_id' => $product->id,
                                'size_id' => $sizeData['size_id'],
                                'stock_quantity' => $sizeData['stock_quantity'] ?? 0,
                                'price_adjustment' => $sizeData['price_adjustment'] ?? 0,
                                'sku_suffix' => $sizeData['sku_suffix'] ?? null,
                                'is_available' => isset($sizeData['is_available']) ? (bool)$sizeData['is_available'] : true,
                            ]);
                        }
                    }
                });

                // Update overall stock status
                $product->updateOverallStockStatus();
            }

            return response()->json([
                'success' => true,
                'message' => 'Product updated successfully!'
            ]);
        } catch (\Illuminate\Database\Eloquent\ModelNotFoundException $e) {
            throw new \InvalidArgumentException('Product not found.');
        } catch (\InvalidArgumentException $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ], 400);
        } catch (\Exception $e) {
            Log::error('Product update failed: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'An unexpected error occurred while updating the product.'
            ], 500);
        }
    }

    public function deleteProduct($id)
    {
        try {
            $product = Product::findOrFail($id);

            // Optional: Check if product has any completed orders (for business logic)
            // Comment out this check if you want to allow deletion of products with order history
            /*
            $completedOrdersCount = $product->orderItems()
                ->whereHas('order', function($query) {
                    $query->whereIn('status', ['delivered', 'shipped']);
                })
                ->count();

            if ($completedOrdersCount > 0) {
                return response()->json([
                    'success' => false,
                    'message' => 'Cannot delete product that has been delivered or shipped. Product has order history.'
                ], 400);
            }
            */

            // Delete images from storage
            if ($product->images && is_array($product->images)) {
                foreach ($product->images as $image) {
                    if ($image && Storage::disk('public')->exists($image)) {
                        Storage::disk('public')->delete($image);
                    }
                }
            }

            // Delete the product (cascade will handle related records)
            $product->delete();

            return response()->json([
                'success' => true,
                'message' => 'Product deleted successfully!'
            ]);
        } catch (\Illuminate\Database\Eloquent\ModelNotFoundException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Product not found.'
            ], 404);
        } catch (\Exception $e) {
            Log::error('Product deletion failed: ' . $e->getMessage(), [
                'product_id' => $id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return response()->json([
                'success' => false,
                'message' => 'An unexpected error occurred while deleting the product: ' . $e->getMessage()
            ], 500);
        }
    }

    public function bulkDeleteProducts(Request $request)
    {
        try {
            $request->validate([
                'product_ids' => 'required|array|min:1',
                'product_ids.*' => 'exists:products,id'
            ]);

            $productIds = $request->product_ids;
            $deletedCount = 0;
            $errors = [];

            foreach ($productIds as $productId) {
                try {
                    $product = Product::findOrFail($productId);

                    // Delete images from storage
                    if ($product->images && is_array($product->images)) {
                        foreach ($product->images as $image) {
                            if ($image && Storage::disk('public')->exists($image)) {
                                Storage::disk('public')->delete($image);
                            }
                        }
                    }

                    // Delete the product (cascade will handle related records)
                    $product->delete();
                    $deletedCount++;

                } catch (\Exception $e) {
                    $errors[] = "Failed to delete product ID {$productId}: " . $e->getMessage();
                    Log::error('Bulk product deletion failed for product: ' . $productId, [
                        'product_id' => $productId,
                        'error' => $e->getMessage()
                    ]);
                }
            }

            if ($deletedCount > 0 && empty($errors)) {
                return response()->json([
                    'success' => true,
                    'message' => "Successfully deleted {$deletedCount} product(s)."
                ]);
            } elseif ($deletedCount > 0 && !empty($errors)) {
                return response()->json([
                    'success' => true,
                    'message' => "Deleted {$deletedCount} product(s). Some products could not be deleted.",
                    'errors' => $errors
                ]);
            } else {
                return response()->json([
                    'success' => false,
                    'message' => 'No products were deleted.',
                    'errors' => $errors
                ], 400);
            }

        } catch (\Illuminate\Validation\ValidationException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Invalid request data.',
                'errors' => $e->errors()
            ], 422);
        } catch (\Exception $e) {
            Log::error('Bulk product deletion failed: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'An unexpected error occurred during bulk deletion.'
            ], 500);
        }
    }

    // Category Management
    public function categories()
    {
        $categories = Category::withCount('products')
            ->orderBy('sort_order')
            ->orderBy('name')
            ->paginate(20);

        return view('admin.categories.index', compact('categories'));
    }

    public function createCategory()
    {
        $parentCategories = Category::active()->ordered()->get();
        $sizes = Size::active()->ordered()->get();

        return view('admin.categories.create', compact('parentCategories', 'sizes'));
    }

    public function storeCategory(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'name' => 'required|string|max:255|unique:categories,name',
                'parent_id' => 'nullable|exists:categories,id',
                'description' => 'nullable|string',
                'image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
                'sort_order' => 'nullable|integer|min:0',
                'is_active' => 'nullable|boolean',
                'is_featured' => 'nullable|boolean',
                'inherit_parent_sizes' => 'nullable|boolean',
                'size_ids' => 'nullable|array',
                'size_ids.*' => 'exists:sizes,id'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            $data = $request->except(['image', 'size_ids']);
            $data['slug'] = Str::slug($request->name);
            $data['is_active'] = $request->has('is_active');
            $data['is_featured'] = $request->has('is_featured');
            $data['inherit_parent_sizes'] = $request->get('inherit_parent_sizes', true);
            $data['sort_order'] = $request->sort_order ?? 0;

            // Handle image upload
            if ($request->hasFile('image')) {
                $path = $request->file('image')->store('categories', 'public');
                $data['image'] = $path;
            }

            // Create category
            $category = Category::create($data);

            // Update hierarchy information
            $category->updateLevel();
            $category->updatePath();

            // Attach sizes if provided
            if ($request->has('size_ids') && is_array($request->size_ids)) {
                $category->sizes()->attach($request->size_ids);
            }

            return response()->json([
                'success' => true,
                'message' => 'Category created successfully!',
                'redirect_url' => route('admin.categories.index')
            ]);
        } catch (\Exception $e) {
            throw new \InvalidArgumentException('Failed to create category: ' . $e->getMessage());
        }
    }

    public function editCategory($id)
    {
        try {
            $category = Category::with(['sizes', 'parent'])->findOrFail($id);
            $parentCategories = Category::active()->ordered()->where('id', '!=', $id)->get();
            $sizes = Size::active()->ordered()->get();
            $categorySizeIds = $category->sizes->pluck('id')->toArray();

            return view('admin.categories.edit', compact('category', 'parentCategories', 'sizes', 'categorySizeIds'));
        } catch (\Exception $e) {
            throw new \InvalidArgumentException('Category not found: ' . $e->getMessage());
        }
    }

    public function updateCategory(Request $request, $id)
    {
        try {
            $category = Category::findOrFail($id);

            $validator = Validator::make($request->all(), [
                'name' => 'required|string|max:255|unique:categories,name,' . $id,
                'description' => 'nullable|string',
                'image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
                'sort_order' => 'nullable|integer|min:0',
                'is_active' => 'nullable|boolean',
                'is_featured' => 'nullable|boolean'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            $data = $request->except(['image']);
            $data['slug'] = Str::slug($request->name);
            $data['is_active'] = $request->has('is_active');
            $data['is_featured'] = $request->has('is_featured');
            $data['sort_order'] = $request->sort_order ?? 0;

            // Handle image upload
            if ($request->hasFile('image')) {
                // Delete old image
                if ($category->image) {
                    Storage::disk('public')->delete($category->image);
                }

                $path = $request->file('image')->store('categories', 'public');
                $data['image'] = $path;
            }

            $category->update($data);

            return response()->json([
                'success' => true,
                'message' => 'Category updated successfully!'
            ]);
        } catch (\Exception $e) {
            throw new \InvalidArgumentException('Failed to update category: ' . $e->getMessage());
        }
    }

    public function deleteCategory($id)
    {
        try {
            $category = Category::findOrFail($id);

            // Check if category has products
            if ($category->products()->count() > 0) {
                throw new \InvalidArgumentException('Cannot delete category that has products. Please move or delete the products first.');
            }

            // Delete image
            if ($category->image) {
                Storage::disk('public')->delete($category->image);
            }

            $category->delete();

            return response()->json([
                'success' => true,
                'message' => 'Category deleted successfully!'
            ]);
        } catch (\Exception $e) {
            throw new \InvalidArgumentException('Failed to delete category: ' . $e->getMessage());
        }
    }

    // Size Management Methods

    public function indexSizes()
    {
        $sizes = Size::with('categories')->ordered()->paginate(20);
        return view('admin.sizes.index', compact('sizes'));
    }

    public function createSize()
    {
        return view('admin.sizes.create');
    }

    public function storeSize(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'name' => 'required|string|max:50',
                'display_name' => 'required|string|max:100',
                'category_type' => 'required|in:clothing,shoes,rings,jewelry,general',
                'description' => 'nullable|string|max:500',
                'sort_order' => 'nullable|integer|min:0',
                'is_active' => 'boolean'
            ]);

            if ($validator->fails()) {
                return redirect()->back()
                    ->withErrors($validator)
                    ->withInput();
            }

            // Check for duplicate name + category_type combination
            $exists = Size::where('name', $request->name)
                         ->where('category_type', $request->category_type)
                         ->exists();

            if ($exists) {
                return redirect()->back()
                    ->withErrors(['name' => 'A size with this name already exists for the selected category type.'])
                    ->withInput();
            }

            Size::create([
                'name' => $request->name,
                'display_name' => $request->display_name,
                'category_type' => $request->category_type,
                'description' => $request->description,
                'sort_order' => $request->sort_order ?? 0,
                'is_active' => $request->boolean('is_active', true)
            ]);

            return redirect()->route('admin.sizes.index')
                ->with('success', 'Size created successfully!');

        } catch (\Exception $e) {
            return redirect()->back()
                ->withErrors(['error' => 'Failed to create size: ' . $e->getMessage()])
                ->withInput();
        }
    }

    public function editSize($id)
    {
        try {
            $size = Size::with('categories')->findOrFail($id);
            return view('admin.sizes.edit', compact('size'));
        } catch (\Exception $e) {
            throw new \InvalidArgumentException('Size not found: ' . $e->getMessage());
        }
    }

    public function updateSize(Request $request, $id)
    {
        try {
            $size = Size::findOrFail($id);

            $validator = Validator::make($request->all(), [
                'name' => 'required|string|max:50',
                'display_name' => 'required|string|max:100',
                'category_type' => 'required|in:clothing,shoes,rings,jewelry,general',
                'description' => 'nullable|string|max:500',
                'sort_order' => 'nullable|integer|min:0',
                'is_active' => 'boolean'
            ]);

            if ($validator->fails()) {
                return redirect()->back()
                    ->withErrors($validator)
                    ->withInput();
            }

            // Check for duplicate name + category_type combination (excluding current size)
            $exists = Size::where('name', $request->name)
                         ->where('category_type', $request->category_type)
                         ->where('id', '!=', $id)
                         ->exists();

            if ($exists) {
                return redirect()->back()
                    ->withErrors(['name' => 'A size with this name already exists for the selected category type.'])
                    ->withInput();
            }

            $size->update([
                'name' => $request->name,
                'display_name' => $request->display_name,
                'category_type' => $request->category_type,
                'description' => $request->description,
                'sort_order' => $request->sort_order ?? 0,
                'is_active' => $request->boolean('is_active', true)
            ]);

            return redirect()->route('admin.sizes.index')
                ->with('success', 'Size updated successfully!');

        } catch (\Exception $e) {
            return redirect()->back()
                ->withErrors(['error' => 'Failed to update size: ' . $e->getMessage()])
                ->withInput();
        }
    }

    public function deleteSize($id)
    {
        try {
            $size = Size::findOrFail($id);

            // Check if size is being used by any products
            $productCount = $size->productSizes()->count();
            if ($productCount > 0) {
                return redirect()->back()
                    ->withErrors(['error' => "Cannot delete size '{$size->display_name}' as it is being used by {$productCount} product(s)."]);
            }

            // Check if size is assigned to any categories
            $categoryCount = $size->categories()->count();
            if ($categoryCount > 0) {
                // Detach from categories first
                $size->categories()->detach();
            }

            $size->delete();

            return redirect()->route('admin.sizes.index')
                ->with('success', 'Size deleted successfully!');

        } catch (\Exception $e) {
            return redirect()->back()
                ->withErrors(['error' => 'Failed to delete size: ' . $e->getMessage()]);
        }
    }

    // API Methods for Category Hierarchy and Sizes

    /**
     * Get category hierarchy for dropdowns
     */
    public function getCategoryHierarchy()
    {
        $categories = Category::with('children.children')
                            ->roots()
                            ->active()
                            ->ordered()
                            ->get();

        return response()->json([
            'success' => true,
            'categories' => $this->formatCategoryHierarchy($categories)
        ]);
    }

    /**
     * Format categories for hierarchical display
     */
    private function formatCategoryHierarchy($categories, $level = 0)
    {
        $formatted = [];

        foreach ($categories as $category) {
            $formatted[] = [
                'id' => $category->id,
                'name' => str_repeat('— ', $level) . $category->name,
                'level' => $level,
                'has_children' => $category->hasChildren(),
            ];

            if ($category->children->isNotEmpty()) {
                $formatted = array_merge($formatted, $this->formatCategoryHierarchy($category->children, $level + 1));
            }
        }

        return $formatted;
    }

    /**
     * Get available sizes for a category
     */
    public function getCategorySizes($categoryId)
    {
        try {
            $category = Category::findOrFail($categoryId);
            $availableSizes = $category->getAvailableSizes();

            return response()->json([
                'success' => true,
                'sizes' => $availableSizes->map(function ($size) {
                    return [
                        'id' => $size->id,
                        'name' => $size->name,
                        'display_name' => $size->full_display_name,
                        'category_type' => $size->category_type,
                    ];
                })
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to load category sizes: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get all available sizes by category type
     */
    public function getSizesByType($categoryType = null)
    {
        try {
            $query = Size::active()->ordered();

            if ($categoryType) {
                $query->forCategoryType($categoryType);
            }

            $sizes = $query->get();

            return response()->json([
                'success' => true,
                'sizes' => $sizes->map(function ($size) {
                    return [
                        'id' => $size->id,
                        'name' => $size->name,
                        'display_name' => $size->full_display_name,
                        'category_type' => $size->category_type,
                    ];
                })
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to load sizes: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Update category sizes
     */
    public function updateCategorySizes(Request $request, $categoryId)
    {
        try {
            $validator = Validator::make($request->all(), [
                'size_ids' => 'required|array',
                'size_ids.*' => 'exists:sizes,id',
                'inherit_parent_sizes' => 'boolean'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            $category = Category::findOrFail($categoryId);

            // Update inheritance setting
            $category->update([
                'inherit_parent_sizes' => $request->get('inherit_parent_sizes', true)
            ]);

            // Sync sizes
            $category->sizes()->sync($request->size_ids);

            return response()->json([
                'success' => true,
                'message' => 'Category sizes updated successfully!'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to update category sizes: ' . $e->getMessage()
            ], 500);
        }
    }
}
